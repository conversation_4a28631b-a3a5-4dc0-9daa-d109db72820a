import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

// Mock Bluetooth Device for web compatibility
class BluetoothDevice {
  final String name;
  final String address;
  final bool isConnected;

  BluetoothDevice({
    required this.name,
    required this.address,
    this.isConnected = false,
  });

  @override
  String toString() => 'BluetoothDevice(name: $name, address: $address)';
}

enum BluetoothState {
  unknown,
  unavailable,
  unauthorized,
  turningOn,
  on,
  turningOff,
  off,
}

enum BluetoothConnectionState {
  disconnected,
  connecting,
  connected,
  disconnecting,
}

class BluetoothProvider extends ChangeNotifier {
  static const String _selectedPrinterKey = 'selected_printer';
  static const String _selectedPrinterNameKey = 'selected_printer_name';

  BluetoothState _bluetoothState = BluetoothState.unknown;
  BluetoothConnectionState _connectionState =
      BluetoothConnectionState.disconnected;
  final List<BluetoothDevice> _availableDevices = [];
  BluetoothDevice? _selectedPrinter;
  bool _isScanning = false;
  String? _errorMessage;
  Timer? _scanTimer;

  // Getters
  BluetoothState get bluetoothState => _bluetoothState;
  BluetoothConnectionState get connectionState => _connectionState;
  List<BluetoothDevice> get availableDevices => _availableDevices;
  BluetoothDevice? get selectedPrinter => _selectedPrinter;
  bool get isScanning => _isScanning;
  String? get errorMessage => _errorMessage;
  bool get isConnected =>
      _connectionState == BluetoothConnectionState.connected;
  bool get isBluetoothAvailable => _bluetoothState == BluetoothState.on;

  BluetoothProvider() {
    _initializeBluetooth();
    _loadSelectedPrinter();
  }

  Future<void> _initializeBluetooth() async {
    try {
      if (kIsWeb) {
        // Web doesn't support Bluetooth Serial
        _bluetoothState = BluetoothState.unavailable;
        _errorMessage = 'Bluetooth is not supported on web platform';
      } else {
        // For mobile platforms, we would initialize flutter_bluetooth_serial here
        // For now, we'll simulate Bluetooth availability
        _bluetoothState = BluetoothState.on;
      }
      notifyListeners();
    } catch (e) {
      _bluetoothState = BluetoothState.unavailable;
      _errorMessage = 'Failed to initialize Bluetooth: $e';
      notifyListeners();
    }
  }

  Future<void> _loadSelectedPrinter() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final address = prefs.getString(_selectedPrinterKey);
      final name = prefs.getString(_selectedPrinterNameKey);

      if (address != null && name != null) {
        _selectedPrinter = BluetoothDevice(
          name: name,
          address: address,
          isConnected: false,
        );
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading selected printer: $e');
    }
  }

  Future<void> startScan() async {
    if (_isScanning || !isBluetoothAvailable) return;

    try {
      _isScanning = true;
      _errorMessage = null;
      _availableDevices.clear();
      notifyListeners();

      if (kIsWeb) {
        // Simulate scanning on web with mock devices
        await _simulateWebScan();
      } else {
        // For mobile platforms, implement real Bluetooth scanning
        await _performRealScan();
      }
    } catch (e) {
      _errorMessage = 'Scan failed: $e';
      _isScanning = false;
      notifyListeners();
    }
  }

  Future<void> _simulateWebScan() async {
    // Simulate scanning delay
    await Future.delayed(const Duration(seconds: 1));

    // Add mock printers
    final mockDevices = [
      BluetoothDevice(name: 'POS Printer 1', address: '00:11:22:33:44:55'),
      BluetoothDevice(name: 'Thermal Printer', address: '00:11:22:33:44:56'),
      BluetoothDevice(name: 'Receipt Printer', address: '00:11:22:33:44:57'),
    ];

    for (final device in mockDevices) {
      await Future.delayed(const Duration(milliseconds: 500));
      _availableDevices.add(device);
      notifyListeners();
    }

    _isScanning = false;
    notifyListeners();
  }

  Future<void> _performRealScan() async {
    // This would implement real Bluetooth scanning for mobile platforms
    // using flutter_bluetooth_serial package

    // For now, simulate with mock data
    await _simulateWebScan();
  }

  void stopScan() {
    _isScanning = false;
    _scanTimer?.cancel();
    notifyListeners();
  }

  Future<void> connectToPrinter(BluetoothDevice device) async {
    if (_connectionState == BluetoothConnectionState.connecting) return;

    try {
      _connectionState = BluetoothConnectionState.connecting;
      _errorMessage = null;
      notifyListeners();

      if (kIsWeb) {
        // Simulate connection on web
        await Future.delayed(const Duration(seconds: 2));
        _connectionState = BluetoothConnectionState.connected;
      } else {
        // Implement real connection for mobile
        await _performRealConnection(device);
      }

      _selectedPrinter = BluetoothDevice(
        name: device.name,
        address: device.address,
        isConnected: true,
      );

      // Save selected printer
      await _saveSelectedPrinter(device);

      notifyListeners();
    } catch (e) {
      _connectionState = BluetoothConnectionState.disconnected;
      _errorMessage = 'Connection failed: $e';
      notifyListeners();
    }
  }

  Future<void> _performRealConnection(BluetoothDevice device) async {
    // This would implement real Bluetooth connection for mobile platforms
    // For now, simulate
    await Future.delayed(const Duration(seconds: 2));
    _connectionState = BluetoothConnectionState.connected;
  }

  Future<void> disconnect() async {
    if (_connectionState == BluetoothConnectionState.disconnecting) return;

    try {
      _connectionState = BluetoothConnectionState.disconnecting;
      notifyListeners();

      // Simulate disconnection delay
      await Future.delayed(const Duration(seconds: 1));

      _connectionState = BluetoothConnectionState.disconnected;
      if (_selectedPrinter != null) {
        _selectedPrinter = BluetoothDevice(
          name: _selectedPrinter!.name,
          address: _selectedPrinter!.address,
          isConnected: false,
        );
      }
      notifyListeners();
    } catch (e) {
      _errorMessage = 'Disconnection failed: $e';
      _connectionState = BluetoothConnectionState.connected;
      notifyListeners();
    }
  }

  Future<void> _saveSelectedPrinter(BluetoothDevice device) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_selectedPrinterKey, device.address);
      await prefs.setString(_selectedPrinterNameKey, device.name);
    } catch (e) {
      debugPrint('Error saving selected printer: $e');
    }
  }

  Future<void> clearSelectedPrinter() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_selectedPrinterKey);
      await prefs.remove(_selectedPrinterNameKey);
      _selectedPrinter = null;
      _connectionState = BluetoothConnectionState.disconnected;
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing selected printer: $e');
    }
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _scanTimer?.cancel();
    super.dispose();
  }
}
