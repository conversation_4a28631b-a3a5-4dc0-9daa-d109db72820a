import 'dart:io';
import 'dart:math';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:provider/provider.dart';
import 'package:flutter_barcode_scanner/flutter_barcode_scanner.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:pos_app/models/product.dart';
import 'package:pos_app/providers/product_provider.dart';
import 'package:pos_app/providers/category_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/screens/add_category_screen.dart';

class ProductFormScreen extends StatefulWidget {
  final Product? product;

  const ProductFormScreen({super.key, this.product});

  @override
  _ProductFormScreenState createState() => _ProductFormScreenState();
}

class _ProductFormScreenState extends State<ProductFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _priceController = TextEditingController();
  final _costPriceController = TextEditingController();
  final _wholesalePriceController = TextEditingController();
  final _minWholesaleQtyController = TextEditingController();
  final _stockController = TextEditingController();
  final _categoryController = TextEditingController();
  final _imageUrlController = TextEditingController();

  final _imagePicker = ImagePicker();

  bool _enableWholesale = false;
  bool _hasExpiration = false;
  DateTime? _expirationDate;
  File? _imageFile;
  String? _selectedImageUrl;
  final bool _isLoading = false;
  bool _isSaving = false;
  bool _imagePickerVisible = false;
  bool _isEditing = false;
  String? _selectedCategory;

  double _profitMargin = 0.0;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.product != null;

    // Load categories when the screen opens
    Future.microtask(() {
      Provider.of<CategoryProvider>(context, listen: false).loadCategories();
    });

    if (_isEditing) {
      // Initialize controllers with existing product data
      _nameController.text = widget.product!.name;
      _barcodeController.text = widget.product!.barcode;
      _priceController.text = widget.product!.price.toString();
      _costPriceController.text = widget.product!.costPrice.toString();
      _wholesalePriceController.text =
          widget.product!.wholesalePrice.toString();
      _enableWholesale = widget.product!.enableWholesale;
      _minWholesaleQtyController.text =
          widget.product!.minWholesaleQty.toString();
      _stockController.text = widget.product!.stock.toString();
      _categoryController.text = widget.product!.category;
      _selectedCategory = widget.product!.category;

      // Handle expiration date
      _hasExpiration = widget.product!.hasExpiration;
      _expirationDate = widget.product!.expirationDate;

      // Handle image
      if (widget.product!.imageUrl != null) {
        _imageUrlController.text = widget.product!.imageUrl!;
        _selectedImageUrl = widget.product!.imageUrl;
      }
    } else {
      // Set default values for new product
      _minWholesaleQtyController.text = '10';
      _stockController.text = '0';
      _costPriceController.text = '0.0';
      _priceController.text = '0.0';
      _wholesalePriceController.text = '0.0';
    }

    // Initial profit margin calculations
    _calculateProfitMargin();

    // Add listeners to update profit margins when values change
    _priceController.addListener(_calculateProfitMargin);
    _costPriceController.addListener(_calculateProfitMargin);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _barcodeController.dispose();
    _priceController.dispose();
    _costPriceController.dispose();
    _wholesalePriceController.dispose();
    _minWholesaleQtyController.dispose();
    _stockController.dispose();
    _categoryController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  // Calculate profit margin based on cost and retail price
  void _calculateProfitMargin() {
    try {
      final costPrice = double.tryParse(_costPriceController.text) ?? 0;
      final retailPrice = double.tryParse(_priceController.text) ?? 0;

      setState(() {
        if (costPrice > 0) {
          _profitMargin = (retailPrice - costPrice) / costPrice;
        } else {
          _profitMargin = 0;
        }
      });
    } catch (e) {
      // Ignore errors during calculation, they'll be handled during validation
    }
  }

  // Helper method to get color based on remaining days until expiration
  Color _getRemainingDaysColor(DateTime expirationDate) {
    final daysRemaining = expirationDate.difference(DateTime.now()).inDays;

    if (daysRemaining < 0) {
      return Colors.red; // Expired
    } else if (daysRemaining < 30) {
      return Colors.orange; // Less than a month left
    } else if (daysRemaining < 90) {
      return Colors.amber; // Less than 3 months left
    } else {
      return Colors.green; // More than 3 months left
    }
  }

  // Helper method to get text for remaining days until expiration
  String _getRemainingDaysText(DateTime expirationDate) {
    final daysRemaining = expirationDate.difference(DateTime.now()).inDays;

    if (daysRemaining < 0) {
      return 'Expired ${-daysRemaining} days ago';
    } else if (daysRemaining == 0) {
      return 'Expires today';
    } else if (daysRemaining == 1) {
      return 'Expires tomorrow';
    } else if (daysRemaining < 30) {
      return '$daysRemaining days left';
    } else if (daysRemaining < 365) {
      final months = (daysRemaining / 30).floor();
      return '$months ${months == 1 ? 'month' : 'months'} left';
    } else {
      final years = (daysRemaining / 365).floor();
      final remainingMonths = ((daysRemaining % 365) / 30).floor();
      return '$years ${years == 1 ? 'year' : 'years'}${remainingMonths > 0 ? ', $remainingMonths ${remainingMonths == 1 ? 'month' : 'months'}' : ''} left';
    }
  }

  // Generate a random barcode (EAN-13 format)
  void _generateBarcode() {
    final random = Random();
    // Generate first 12 digits (13th will be checksum)
    String barcode = '';
    for (int i = 0; i < 12; i++) {
      barcode += random.nextInt(10).toString();
    }

    // Calculate checksum digit (EAN-13)
    int sum = 0;
    for (int i = 0; i < 12; i++) {
      int digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit : digit * 3;
    }
    int checkDigit = (10 - (sum % 10)) % 10;
    barcode += checkDigit.toString();

    setState(() {
      _barcodeController.text = barcode;
    });
  }

  // Enhanced barcode scanning that opens camera directly
  Future<void> _scanBarcode() async {
    try {
      final barcode = await FlutterBarcodeScanner.scanBarcode(
        '#FF6750A4', // Use theme primary color
        'Cancel',
        true,
        ScanMode.BARCODE,
      );

      if (barcode != '-1') {
        setState(() {
          _barcodeController.text = barcode;
          // Show success feedback
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 10),
                  Text('Barcode scanned successfully'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        });
      }
    } on PlatformException {
      setState(() {
        // Show error feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                SizedBox(width: 10),
                Text('Failed to scan barcode'),
              ],
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      });
    }
  }

  // Helper method to build image placeholder
  Widget _buildImagePlaceholder() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(FontAwesomeIcons.image, color: Colors.grey.shade400, size: 40),
          SizedBox(height: 8),
          Text(
            'Tap to add image',
            style: GoogleFonts.poppins(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build image source button
  Widget _buildImageSourceButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3), width: 1),
        ),
        child: Column(
          children: [
            FaIcon(icon, color: color, size: 24),
            SizedBox(height: 8),
            Text(
              label,
              style: GoogleFonts.poppins(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Pick image from gallery
  Future<void> _pickImageFromGallery() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        _cropImage(pickedFile);
      }
    } catch (e) {
      print('Error picking image from gallery: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _imagePickerVisible = false;
      });
    }
  }

  // Pick image from camera
  Future<void> _pickImageFromCamera() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        _cropImage(pickedFile);
      }
    } catch (e) {
      print('Error picking image from camera: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to capture image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _imagePickerVisible = false;
      });
    }
  }

  // Crop the picked image
  Future<void> _cropImage(XFile pickedFile) async {
    try {
      final imagePath = pickedFile.path;

      if (kIsWeb) {
        setState(() {
          _selectedImageUrl = imagePath;
          _imageUrlController.text = imagePath;
        });
        return;
      }

      final croppedFile = await ImageCropper().cropImage(
        sourcePath: imagePath,
        // تم إزالة المعاملات القديمة في الإصدار الجديد
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'Crop Image',
            toolbarColor: Colors.blue,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: false,
          ),
          IOSUiSettings(minimumAspectRatio: 1.0, aspectRatioLockEnabled: false),
        ],
      );

      if (croppedFile != null) {
        setState(() {
          _imageFile = File(croppedFile.path);
          _selectedImageUrl = croppedFile.path;
          _imageUrlController.text = '';
        });
      }
    } catch (e) {
      print('Error cropping image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to crop image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Save the image to app directory and return the file path
  Future<String?> _saveImageToAppDirectory() async {
    if (_imageFile == null) {
      return _selectedImageUrl;
    }

    try {
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = 'product_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final savedImage = await _imageFile!.copy('${appDir.path}/$fileName');
      return savedImage.path;
    } catch (e) {
      print('Error saving image: $e');
      return null;
    }
  }

  // Save product to database
  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // Save image if available
      final savedImagePath = await _saveImageToAppDirectory();

      // Get all form values
      final name = _nameController.text.trim();
      final barcode = _barcodeController.text.trim();
      final price = double.parse(_priceController.text);
      final costPrice = double.parse(_costPriceController.text);
      final wholesalePrice = double.parse(_wholesalePriceController.text);
      final minWholesaleQty = int.parse(_minWholesaleQtyController.text);
      final stock = int.parse(_stockController.text);
      final category = _selectedCategory ?? '';
      final imageUrl =
          savedImagePath ??
          (_imageUrlController.text.isEmpty ? null : _imageUrlController.text);

      final productProvider = Provider.of<ProductProvider>(
        context,
        listen: false,
      );

      if (_isEditing) {
        // Update existing product
        final product = Product(
          id: widget.product!.id,
          name: name,
          barcode: barcode,
          price: price,
          costPrice: costPrice,
          wholesalePrice: wholesalePrice,
          enableWholesale: _enableWholesale,
          minWholesaleQty: minWholesaleQty,
          stock: stock,
          category: category,
          imageUrl: imageUrl,
          hasExpiration: _hasExpiration,
          expirationDate: _expirationDate,
        );

        await productProvider.updateProduct(product);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 10),
                  Text('Product updated successfully'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        // Create new product
        final product = Product(
          name: name,
          barcode: barcode,
          price: price,
          costPrice: costPrice,
          wholesalePrice: wholesalePrice,
          enableWholesale: _enableWholesale,
          minWholesaleQty: minWholesaleQty,
          stock: stock,
          category: category,
          imageUrl: imageUrl,
          hasExpiration: _hasExpiration,
          expirationDate: _expirationDate,
        );

        await productProvider.addProduct(product);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 10),
                  Text('Product added successfully'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      print('Error saving product: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                SizedBox(width: 10),
                Text('Error saving product: ${e.toString()}'),
              ],
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Widget _buildCategoryDropdown(BuildContext context) {
    return Consumer2<CategoryProvider, LocaleProvider>(
      builder: (context, categoryProvider, localeProvider, _) {
        final categories = categoryProvider.categories;

        return FormField<String>(
          initialValue: _selectedCategory,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return localeProvider.isRTL
                  ? 'يرجى اختيار فئة المنتج'
                  : 'Please select a product category';
            }
            return null;
          },
          builder: (FormFieldState<String> state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: InputDecoration(
                    labelText: localeProvider.isRTL ? 'الفئة*' : 'Category*',
                    hintText:
                        localeProvider.isRTL
                            ? 'اختر فئة المنتج'
                            : 'Select product category',
                    prefixIcon: const Icon(Icons.category),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    errorText: state.errorText,
                  ),
                  items: [
                    // Regular categories
                    ...categories.map(
                      (category) => DropdownMenuItem<String>(
                        value: category.name,
                        child: Row(
                          children: [
                            Icon(
                              _getIconFromName(category.iconName ?? 'category'),
                              size: 20,
                              color: Theme.of(context).primaryColor,
                            ),
                            const SizedBox(width: 8),
                            Text(category.name),
                          ],
                        ),
                      ),
                    ),
                    // Add New Category option
                    DropdownMenuItem<String>(
                      value: '__add_new__',
                      child: Row(
                        children: [
                          Icon(
                            Icons.add_circle_outline,
                            size: 20,
                            color: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            localeProvider.isRTL
                                ? 'إضافة فئة جديدة'
                                : 'Add New Category',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  onChanged: (String? newValue) async {
                    if (newValue == '__add_new__') {
                      // Navigate to add category screen
                      final result = await Navigator.of(context).push<bool>(
                        MaterialPageRoute(
                          builder: (context) => const AddCategoryScreen(),
                        ),
                      );

                      if (result == true) {
                        // Refresh categories and don't change selection
                        await categoryProvider.loadCategories();
                      }
                    } else {
                      setState(() {
                        _selectedCategory = newValue;
                        _categoryController.text = newValue ?? '';
                      });
                      state.didChange(newValue);
                    }
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  IconData _getIconFromName(String iconName) {
    switch (iconName) {
      case 'local_cafe':
        return Icons.local_cafe;
      case 'bakery_dining':
        return Icons.bakery_dining;
      case 'lunch_dining':
        return Icons.lunch_dining;
      case 'restaurant':
        return Icons.restaurant;
      case 'egg':
        return Icons.egg;
      case 'eco':
        return Icons.eco;
      case 'shopping_cart':
        return Icons.shopping_cart;
      case 'local_grocery_store':
        return Icons.local_grocery_store;
      case 'fastfood':
        return Icons.fastfood;
      case 'cake':
        return Icons.cake;
      case 'wine_bar':
        return Icons.wine_bar;
      case 'icecream':
        return Icons.icecream;
      case 'local_pizza':
        return Icons.local_pizza;
      case 'ramen_dining':
        return Icons.ramen_dining;
      case 'set_meal':
        return Icons.set_meal;
      default:
        return Icons.category;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final currencyFormatter = NumberFormat.currency(
      symbol: 'DA ',
      decimalDigits: 2,
    );
    final percentFormatter = NumberFormat.percentPattern();
    percentFormatter.maximumFractionDigits = 1;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditing ? 'Edit Product' : 'Add Product',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        actions: [
          if (_isEditing)
            IconButton(
              icon: Icon(Icons.delete, color: Colors.red),
              onPressed: () {
                // Show delete confirmation dialog
                showDialog(
                  context: context,
                  builder:
                      (context) => AlertDialog(
                        title: Text('Confirm Delete'),
                        content: Text(
                          'Are you sure you want to delete this product?',
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: Text('Cancel'),
                          ),
                          TextButton(
                            onPressed: () async {
                              Navigator.pop(context);
                              try {
                                final productProvider =
                                    Provider.of<ProductProvider>(
                                      context,
                                      listen: false,
                                    );
                                await productProvider.deleteProduct(
                                  widget.product!.id!,
                                );
                                if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Product deleted successfully',
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  Navigator.pop(context);
                                }
                              } catch (e) {
                                if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Failed to delete product: $e',
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              }
                            },
                            child: Text(
                              'Delete',
                              style: TextStyle(color: Colors.red),
                            ),
                          ),
                        ],
                      ),
                );
              },
            ),
        ],
      ),
      body:
          _isSaving
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Saving product...',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              )
              : Form(
                key: _formKey,
                child: Stack(
                  children: [
                    // Main Form
                    SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Image Selection Card
                          Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.image,
                                        color: colorScheme.primary,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Product Image',
                                        style: GoogleFonts.poppins(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: colorScheme.primary,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 16),
                                  Center(
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _imagePickerVisible =
                                              !_imagePickerVisible;
                                        });
                                      },
                                      child: Container(
                                        width: 200,
                                        height: 200,
                                        decoration: BoxDecoration(
                                          color: Colors.grey[200],
                                          borderRadius: BorderRadius.circular(
                                            10,
                                          ),
                                          border: Border.all(
                                            color: colorScheme.outline
                                                .withOpacity(0.5),
                                            width: 2,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withOpacity(
                                                0.1,
                                              ),
                                              blurRadius: 10,
                                              offset: Offset(0, 5),
                                            ),
                                          ],
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          child:
                                              _selectedImageUrl != null ||
                                                      _imageUrlController
                                                          .text
                                                          .isNotEmpty
                                                  ? kIsWeb
                                                      ? Image.network(
                                                        _selectedImageUrl ??
                                                            _imageUrlController
                                                                .text,
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (
                                                          context,
                                                          error,
                                                          stackTrace,
                                                        ) {
                                                          return _buildImagePlaceholder();
                                                        },
                                                      )
                                                      : _imageFile != null
                                                      ? Image.file(
                                                        _imageFile!,
                                                        fit: BoxFit.cover,
                                                      )
                                                      : _selectedImageUrl !=
                                                          null
                                                      ? Image.file(
                                                        File(
                                                          _selectedImageUrl!,
                                                        ),
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (
                                                          context,
                                                          error,
                                                          stackTrace,
                                                        ) {
                                                          return Image.network(
                                                            _selectedImageUrl!,
                                                            fit: BoxFit.cover,
                                                            errorBuilder: (
                                                              context,
                                                              error,
                                                              stackTrace,
                                                            ) {
                                                              return _buildImagePlaceholder();
                                                            },
                                                          );
                                                        },
                                                      )
                                                      : Image.network(
                                                        _imageUrlController
                                                            .text,
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (
                                                          context,
                                                          error,
                                                          stackTrace,
                                                        ) {
                                                          return _buildImagePlaceholder();
                                                        },
                                                      )
                                                  : _buildImagePlaceholder(),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 16),
                                  if (_imagePickerVisible)
                                    Container(
                                      padding: EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color:
                                            colorScheme.surfaceContainerHighest,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.stretch,
                                        children: [
                                          Text(
                                            'Add Product Image',
                                            style: GoogleFonts.poppins(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                          SizedBox(height: 8),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceEvenly,
                                            children: [
                                              _buildImageSourceButton(
                                                icon: FontAwesomeIcons.camera,
                                                label: 'Camera',
                                                onTap: _pickImageFromCamera,
                                                color: Colors.blue,
                                              ),
                                              _buildImageSourceButton(
                                                icon: FontAwesomeIcons.image,
                                                label: 'Gallery',
                                                onTap: _pickImageFromGallery,
                                                color: Colors.green,
                                              ),
                                              _buildImageSourceButton(
                                                icon: FontAwesomeIcons.xmark,
                                                label: 'Cancel',
                                                onTap: () {
                                                  setState(() {
                                                    _imagePickerVisible = false;
                                                  });
                                                },
                                                color: Colors.red,
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    )
                                  else
                                    TextFormField(
                                      controller: _imageUrlController,
                                      decoration: InputDecoration(
                                        labelText: 'Image URL',
                                        hintText:
                                            'Enter URL or tap image to upload',
                                        prefixIcon: Icon(Icons.link),
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),

                          SizedBox(height: 16),

                          // Basic Info Card
                          Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.circleInfo,
                                        color: colorScheme.primary,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Basic Information',
                                        style: GoogleFonts.poppins(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: colorScheme.primary,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 16),
                                  TextFormField(
                                    controller: _nameController,
                                    decoration: InputDecoration(
                                      labelText: 'Product Name*',
                                      hintText: 'Enter product name',
                                      prefixIcon: Icon(Icons.shopping_bag),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter product name';
                                      }
                                      return null;
                                    },
                                  ),
                                  SizedBox(height: 16),
                                  _buildCategoryDropdown(context),
                                  SizedBox(height: 16),
                                  TextFormField(
                                    controller: _stockController,
                                    decoration: InputDecoration(
                                      labelText: 'Stock Quantity*',
                                      hintText: 'Enter available quantity',
                                      prefixIcon: Icon(Icons.inventory),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    keyboardType: TextInputType.number,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter stock quantity';
                                      }
                                      return null;
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),

                          SizedBox(height: 16),

                          // Barcode Card
                          Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.barcode,
                                        color: colorScheme.primary,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Barcode',
                                        style: GoogleFonts.poppins(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: colorScheme.primary,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 16),
                                  TextFormField(
                                    controller: _barcodeController,
                                    decoration: InputDecoration(
                                      labelText: 'Barcode',
                                      hintText: 'Enter barcode or scan',
                                      prefixIcon: Icon(Icons.qr_code),
                                      suffixIcon: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          IconButton(
                                            icon: Icon(
                                              Icons.camera_alt,
                                              color: colorScheme.primary,
                                            ),
                                            onPressed: _scanBarcode,
                                            tooltip: 'Scan barcode',
                                          ),
                                          IconButton(
                                            icon: Icon(
                                              Icons.refresh,
                                              color: colorScheme.primary,
                                            ),
                                            onPressed: _generateBarcode,
                                            tooltip: 'Generate barcode',
                                          ),
                                        ],
                                      ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Pricing Card
                          Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.dollarSign,
                                        color: colorScheme.primary,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Pricing',
                                        style: GoogleFonts.poppins(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: colorScheme.primary,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 16),

                                  // Purchase Cost Price
                                  TextFormField(
                                    controller: _costPriceController,
                                    decoration: InputDecoration(
                                      labelText: 'Purchase Cost Price*',
                                      hintText: 'Enter purchase cost price',
                                      prefixIcon: Icon(Icons.money),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                          decimal: true,
                                        ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                        RegExp(r'(^\d*\.?\d{0,2})'),
                                      ),
                                    ],
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter purchase cost price';
                                      }
                                      return null;
                                    },
                                    onChanged: (value) {
                                      setState(() {
                                        _calculateProfitMargin();
                                      });
                                    },
                                  ),
                                  SizedBox(height: 16),

                                  // Retail Price
                                  TextFormField(
                                    controller: _priceController,
                                    decoration: InputDecoration(
                                      labelText: 'Retail Price*',
                                      hintText: 'Enter retail price',
                                      prefixIcon: Icon(Icons.attach_money),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                          decimal: true,
                                        ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                        RegExp(r'(^\d*\.?\d{0,2})'),
                                      ),
                                    ],
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter retail price';
                                      }
                                      return null;
                                    },
                                    onChanged: (value) {
                                      setState(() {
                                        _calculateProfitMargin();
                                      });
                                    },
                                  ),
                                  SizedBox(height: 12),

                                  // Profit Margin Display
                                  if (_costPriceController.text.isNotEmpty &&
                                      _priceController.text.isNotEmpty)
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 12,
                                      ),
                                      decoration: BoxDecoration(
                                        color:
                                            _profitMargin > 0.3
                                                ? Colors.green.shade50
                                                : Colors.orange.shade50,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color:
                                              _profitMargin > 0.3
                                                  ? Colors.green
                                                  : Colors.orange,
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Profit Margin:',
                                            style: GoogleFonts.poppins(
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          Text(
                                            percentFormatter.format(
                                              _profitMargin,
                                            ),
                                            style: GoogleFonts.poppins(
                                              fontWeight: FontWeight.w700,
                                              color:
                                                  _profitMargin > 0.3
                                                      ? Colors.green
                                                      : Colors.orange,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),

                                  SizedBox(height: 16),

                                  // Wholesale Toggle Switch
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Enable Wholesale Pricing',
                                        style: GoogleFonts.poppins(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      Switch(
                                        value: _enableWholesale,
                                        onChanged: (value) {
                                          setState(() {
                                            _enableWholesale = value;
                                          });
                                        },
                                        activeColor: colorScheme.primary,
                                      ),
                                    ],
                                  ),

                                  // Wholesale Price Fields (if enabled)
                                  if (_enableWholesale) ...[
                                    SizedBox(height: 16),
                                    TextFormField(
                                      controller: _wholesalePriceController,
                                      decoration: InputDecoration(
                                        labelText: 'Wholesale Price*',
                                        hintText: 'Enter wholesale price',
                                        prefixIcon: Icon(Icons.storefront),
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                      ),
                                      keyboardType:
                                          TextInputType.numberWithOptions(
                                            decimal: true,
                                          ),
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                          RegExp(r'(^\d*\.?\d{0,2})'),
                                        ),
                                      ],
                                      validator: (value) {
                                        if (_enableWholesale &&
                                            (value == null || value.isEmpty)) {
                                          return 'Please enter wholesale price';
                                        }
                                        return null;
                                      },
                                    ),
                                    SizedBox(height: 16),
                                    TextFormField(
                                      controller: _minWholesaleQtyController,
                                      decoration: InputDecoration(
                                        labelText:
                                            'Minimum Wholesale Quantity*',
                                        hintText:
                                            'Enter minimum quantity for wholesale',
                                        prefixIcon: Icon(
                                          Icons.production_quantity_limits,
                                        ),
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                      ),
                                      keyboardType: TextInputType.number,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly,
                                      ],
                                      validator: (value) {
                                        if (_enableWholesale &&
                                            (value == null || value.isEmpty)) {
                                          return 'Please enter minimum wholesale quantity';
                                        }
                                        return null;
                                      },
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                          // Expiration Date Card
                          Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.calendarXmark,
                                        color: colorScheme.primary,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Expiration',
                                        style: GoogleFonts.poppins(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: colorScheme.primary,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 16),

                                  // Has Expiration Toggle
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Product has expiration date',
                                        style: GoogleFonts.poppins(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      Switch(
                                        value: _hasExpiration,
                                        onChanged: (value) {
                                          setState(() {
                                            _hasExpiration = value;
                                            if (!value) {
                                              _expirationDate = null;
                                            } else {
                                              _expirationDate ??= DateTime.now()
                                                  .add(Duration(days: 30));
                                            }
                                          });
                                        },
                                        activeColor: colorScheme.primary,
                                      ),
                                    ],
                                  ),

                                  // Expiration Date Picker (if has expiration)
                                  if (_hasExpiration) ...[
                                    SizedBox(height: 16),
                                    InkWell(
                                      onTap: () async {
                                        final DateTime?
                                        picked = await showDatePicker(
                                          context: context,
                                          initialDate:
                                              _expirationDate ??
                                              DateTime.now().add(
                                                Duration(days: 30),
                                              ),
                                          firstDate: DateTime.now(),
                                          lastDate: DateTime.now().add(
                                            Duration(days: 3650),
                                          ),
                                          builder: (context, child) {
                                            return Theme(
                                              data: Theme.of(context).copyWith(
                                                colorScheme: ColorScheme.light(
                                                  primary: colorScheme.primary,
                                                  onPrimary: Colors.white,
                                                  surface: Colors.white,
                                                  onSurface: Colors.black,
                                                ),
                                              ),
                                              child: child!,
                                            );
                                          },
                                        );

                                        if (picked != null) {
                                          setState(() {
                                            _expirationDate = picked;
                                          });
                                        }
                                      },
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 15,
                                        ),
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: Colors.grey.shade400,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            Icon(
                                              Icons.calendar_today,
                                              color: colorScheme.primary,
                                            ),
                                            SizedBox(width: 12),
                                            Expanded(
                                              child: Text(
                                                _expirationDate != null
                                                    ? DateFormat(
                                                      'yyyy-MM-dd',
                                                    ).format(_expirationDate!)
                                                    : 'Select Expiration Date',
                                                style: GoogleFonts.poppins(
                                                  fontSize: 16,
                                                  color:
                                                      _expirationDate != null
                                                          ? Colors.black
                                                          : Colors
                                                              .grey
                                                              .shade600,
                                                ),
                                              ),
                                            ),
                                            Icon(
                                              Icons.arrow_drop_down,
                                              color: Colors.grey.shade600,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),

                                    // Display time remaining until expiration
                                    if (_expirationDate != null) ...[
                                      SizedBox(height: 12),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 12,
                                        ),
                                        decoration: BoxDecoration(
                                          color: _getRemainingDaysColor(
                                            _expirationDate!,
                                          ).withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          border: Border.all(
                                            color: _getRemainingDaysColor(
                                              _expirationDate!,
                                            ),
                                            width: 1,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              'Time until expiration:',
                                              style: GoogleFonts.poppins(
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            Text(
                                              _getRemainingDaysText(
                                                _expirationDate!,
                                              ),
                                              style: GoogleFonts.poppins(
                                                fontWeight: FontWeight.w700,
                                                color: _getRemainingDaysColor(
                                                  _expirationDate!,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ],
                                ],
                              ),
                            ),
                          ),

                          SizedBox(height: 24),

                          // Save Button
                          ElevatedButton(
                            onPressed: _saveProduct,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: colorScheme.primary,
                              foregroundColor: Colors.white,
                              padding: EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 3,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.save),
                                SizedBox(width: 8),
                                Text(
                                  _isEditing
                                      ? 'Update Product'
                                      : 'Save Product',
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          SizedBox(height: 40),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
    );
  }
}
