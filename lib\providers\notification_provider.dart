import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:io';

enum NotificationType {
  lowStock,
  outOfStock,
  saleCompleted,
  backupCompleted,
  backupFailed,
  systemAlert,
  reminder,
}

class AppNotification {
  final String id;
  final String title;
  final String body;
  final NotificationType type;
  final DateTime createdAt;
  final bool isRead;
  final Map<String, dynamic>? data;

  AppNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.type,
    required this.createdAt,
    this.isRead = false,
    this.data,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'body': body,
    'type': type.name,
    'createdAt': createdAt.toIso8601String(),
    'isRead': isRead,
    'data': data,
  };

  factory AppNotification.fromJson(Map<String, dynamic> json) => AppNotification(
    id: json['id'],
    title: json['title'],
    body: json['body'],
    type: NotificationType.values.firstWhere((e) => e.name == json['type']),
    createdAt: DateTime.parse(json['createdAt']),
    isRead: json['isRead'] ?? false,
    data: json['data'],
  );

  AppNotification copyWith({
    String? id,
    String? title,
    String? body,
    NotificationType? type,
    DateTime? createdAt,
    bool? isRead,
    Map<String, dynamic>? data,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
    );
  }
}

class NotificationProvider extends ChangeNotifier {
  static const String _notificationsKey = 'app_notifications';
  static const String _settingsKey = 'notification_settings';

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  List<AppNotification> _notifications = [];
  bool _isInitialized = false;
  
  // Notification settings
  bool _lowStockEnabled = true;
  bool _saleNotificationsEnabled = true;
  bool _backupNotificationsEnabled = true;
  bool _systemAlertsEnabled = true;
  int _lowStockThreshold = 10;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  // Getters
  List<AppNotification> get notifications => _notifications;
  List<AppNotification> get unreadNotifications => 
      _notifications.where((n) => !n.isRead).toList();
  int get unreadCount => unreadNotifications.length;
  bool get isInitialized => _isInitialized;
  
  // Settings getters
  bool get lowStockEnabled => _lowStockEnabled;
  bool get saleNotificationsEnabled => _saleNotificationsEnabled;
  bool get backupNotificationsEnabled => _backupNotificationsEnabled;
  bool get systemAlertsEnabled => _systemAlertsEnabled;
  int get lowStockThreshold => _lowStockThreshold;
  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;

  NotificationProvider() {
    _initializeNotifications();
    _loadNotifications();
    _loadSettings();
  }

  Future<void> _initializeNotifications() async {
    try {
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Request permissions for Android 13+
      if (Platform.isAndroid) {
        await _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>()
            ?.requestNotificationsPermission();
      }

      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing notifications: $e');
    }
  }

  void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap
    final notificationId = response.payload;
    if (notificationId != null) {
      markAsRead(notificationId);
    }
  }

  Future<void> _loadNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getString(_notificationsKey);
      if (notificationsJson != null) {
        final List<dynamic> notificationsList = jsonDecode(notificationsJson);
        _notifications = notificationsList
            .map((json) => AppNotification.fromJson(json))
            .toList();
        _notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading notifications: $e');
    }
  }

  Future<void> _saveNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = jsonEncode(
        _notifications.map((notification) => notification.toJson()).toList(),
      );
      await prefs.setString(_notificationsKey, notificationsJson);
    } catch (e) {
      debugPrint('Error saving notifications: $e');
    }
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _lowStockEnabled = prefs.getBool('low_stock_enabled') ?? true;
      _saleNotificationsEnabled = prefs.getBool('sale_notifications_enabled') ?? true;
      _backupNotificationsEnabled = prefs.getBool('backup_notifications_enabled') ?? true;
      _systemAlertsEnabled = prefs.getBool('system_alerts_enabled') ?? true;
      _lowStockThreshold = prefs.getInt('low_stock_threshold') ?? 10;
      _soundEnabled = prefs.getBool('sound_enabled') ?? true;
      _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('low_stock_enabled', _lowStockEnabled);
      await prefs.setBool('sale_notifications_enabled', _saleNotificationsEnabled);
      await prefs.setBool('backup_notifications_enabled', _backupNotificationsEnabled);
      await prefs.setBool('system_alerts_enabled', _systemAlertsEnabled);
      await prefs.setInt('low_stock_threshold', _lowStockThreshold);
      await prefs.setBool('sound_enabled', _soundEnabled);
      await prefs.setBool('vibration_enabled', _vibrationEnabled);
    } catch (e) {
      debugPrint('Error saving notification settings: $e');
    }
  }

  Future<void> showNotification({
    required String title,
    required String body,
    required NotificationType type,
    Map<String, dynamic>? data,
    bool saveToHistory = true,
  }) async {
    if (!_isInitialized) return;

    // Check if this type of notification is enabled
    if (!_isNotificationTypeEnabled(type)) return;

    try {
      final notificationId = DateTime.now().millisecondsSinceEpoch;
      
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'pos_app_channel',
        'POS App Notifications',
        channelDescription: 'Notifications for POS App',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _flutterLocalNotificationsPlugin.show(
        notificationId,
        title,
        body,
        platformChannelSpecifics,
        payload: notificationId.toString(),
      );

      // Save to history if requested
      if (saveToHistory) {
        final notification = AppNotification(
          id: notificationId.toString(),
          title: title,
          body: body,
          type: type,
          createdAt: DateTime.now(),
          data: data,
        );

        _notifications.insert(0, notification);
        
        // Keep only last 100 notifications
        if (_notifications.length > 100) {
          _notifications = _notifications.take(100).toList();
        }

        await _saveNotifications();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error showing notification: $e');
    }
  }

  bool _isNotificationTypeEnabled(NotificationType type) {
    switch (type) {
      case NotificationType.lowStock:
      case NotificationType.outOfStock:
        return _lowStockEnabled;
      case NotificationType.saleCompleted:
        return _saleNotificationsEnabled;
      case NotificationType.backupCompleted:
      case NotificationType.backupFailed:
        return _backupNotificationsEnabled;
      case NotificationType.systemAlert:
      case NotificationType.reminder:
        return _systemAlertsEnabled;
    }
  }

  Future<void> markAsRead(String notificationId) async {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      await _saveNotifications();
      notifyListeners();
    }
  }

  Future<void> markAllAsRead() async {
    _notifications = _notifications.map((n) => n.copyWith(isRead: true)).toList();
    await _saveNotifications();
    notifyListeners();
  }

  Future<void> deleteNotification(String notificationId) async {
    _notifications.removeWhere((n) => n.id == notificationId);
    await _saveNotifications();
    notifyListeners();
  }

  Future<void> clearAllNotifications() async {
    _notifications.clear();
    await _saveNotifications();
    notifyListeners();
  }

  // Settings methods
  Future<void> setLowStockEnabled(bool enabled) async {
    _lowStockEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setSaleNotificationsEnabled(bool enabled) async {
    _saleNotificationsEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setBackupNotificationsEnabled(bool enabled) async {
    _backupNotificationsEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setSystemAlertsEnabled(bool enabled) async {
    _systemAlertsEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setLowStockThreshold(int threshold) async {
    _lowStockThreshold = threshold;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> setVibrationEnabled(bool enabled) async {
    _vibrationEnabled = enabled;
    await _saveSettings();
    notifyListeners();
  }

  // Convenience methods for common notifications
  Future<void> showLowStockAlert(String productName, int currentStock) async {
    await showNotification(
      title: 'Low Stock Alert',
      body: '$productName is running low (${currentStock} remaining)',
      type: NotificationType.lowStock,
      data: {'productName': productName, 'currentStock': currentStock},
    );
  }

  Future<void> showOutOfStockAlert(String productName) async {
    await showNotification(
      title: 'Out of Stock',
      body: '$productName is out of stock',
      type: NotificationType.outOfStock,
      data: {'productName': productName},
    );
  }

  Future<void> showSaleCompletedNotification(double amount, String currency) async {
    await showNotification(
      title: 'Sale Completed',
      body: 'Sale completed for $currency $amount',
      type: NotificationType.saleCompleted,
      data: {'amount': amount, 'currency': currency},
    );
  }

  Future<void> showBackupCompletedNotification() async {
    await showNotification(
      title: 'Backup Completed',
      body: 'Database backup completed successfully',
      type: NotificationType.backupCompleted,
    );
  }

  Future<void> showBackupFailedNotification(String error) async {
    await showNotification(
      title: 'Backup Failed',
      body: 'Database backup failed: $error',
      type: NotificationType.backupFailed,
      data: {'error': error},
    );
  }
}
