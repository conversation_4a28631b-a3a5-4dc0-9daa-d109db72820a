import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/cart_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/providers/auth_provider.dart';
import 'package:pos_app/providers/onboarding_provider.dart';
import 'package:pos_app/providers/theme_provider.dart';
import 'package:pos_app/providers/bluetooth_provider.dart';
import 'package:pos_app/providers/session_provider.dart';
import 'package:pos_app/providers/localization_provider.dart';
import 'package:pos_app/providers/currency_provider.dart';
import 'package:pos_app/providers/notification_provider.dart';
import 'package:pos_app/providers/store_settings_provider.dart';
import 'package:pos_app/providers/backup_provider.dart';
import 'package:pos_app/screens/splash_screen.dart';
import 'package:pos_app/config/app_theme.dart';
import 'package:pos_app/services/stock_monitoring_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const MyApp());
}

class AppInitializer extends StatefulWidget {
  final Widget child;

  const AppInitializer({super.key, required this.child});

  @override
  State<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeServices();
    });
  }

  void _initializeServices() {
    final notificationProvider = Provider.of<NotificationProvider>(
      context,
      listen: false,
    );
    StockMonitoringService().initialize(notificationProvider);
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LocaleProvider()),
        ChangeNotifierProvider(create: (_) => LocalizationProvider()),
        ChangeNotifierProvider(create: (_) => CurrencyProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => BluetoothProvider()),
        ChangeNotifierProvider(create: (_) => SessionProvider()),
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => OnboardingProvider()),
        ChangeNotifierProvider(create: (_) => CartProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
        ChangeNotifierProvider(create: (_) => StoreSettingsProvider()),
        ChangeNotifierProvider(create: (_) => BackupProvider()),
      ],
      child: Consumer2<LocaleProvider, ThemeProvider>(
        builder:
            (context, localeProvider, themeProvider, _) => AppInitializer(
              child: MaterialApp(
                title: 'POS App',
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: themeProvider.themeMode,
                home: const SplashScreen(),
                debugShowCheckedModeBanner: false,
                // دعم اللغة العربية وتنسيق RTL
                locale: localeProvider.locale,
                localizationsDelegates: const [
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                supportedLocales: const [
                  Locale('ar', 'DZ'), // العربية/الجزائر
                  Locale('en', 'US'), // الإنجليزية/الولايات المتحدة
                  Locale('fr', 'FR'), // الفرنسية/فرنسا
                ],
              ),
            ),
      ),
    );
  }
}

class TestScreen extends StatelessWidget {
  const TestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('POS App - Test'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.point_of_sale, size: 100, color: Colors.green),
            SizedBox(height: 20),
            Text(
              'مرحباً بك في نظام نقاط البيع',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Text(
              'Welcome to POS System',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
