name: pos_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  # إضافة دعم للغة العربية - Arabic language support
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  
  # حزم الطباعة الحرارية والبلوتوث - مؤقتاً معطلة للبناء
  # bluetooth_thermal_printer: 0.0.6
  # flutter_bluetooth_basic: 0.1.7
  # flutter_bluetooth_serial: 0.4.0
  
  # حزم إنشاء PDF والطباعة - تم اختيار إصدارات متوافقة
  pdf: ^3.11.3
  printing: ^5.14.2
  
  # حزمة المشاركة - تم تحديد إصدار متوافق
  share_plus: ^11.0.0
  
  # حزم أساسية متوافقة
  image: ^4.5.4
  http: ^1.4.0
  provider: ^6.1.5
  intl: ^0.19.0
  sqflite: ^2.4.2
  shared_preferences: ^2.5.3
  path: ^1.9.0
  permission_handler: ^11.3.1
  flutter_secure_storage: ^9.2.2
  crypto: ^3.0.5
  path_provider: ^2.1.5
  # flutter_local_notifications: ^17.2.3
  # googleapis: ^13.2.0
  # googleapis_auth: ^1.6.0
  # file_picker: ^8.1.2
  # google_sign_in: ^6.2.1
  # extension_google_sign_in_as_googleapis_auth: ^2.0.12
  
  # حزم واجهة المستخدم
  flutter_barcode_scanner: 2.0.0
  font_awesome_flutter: ^10.8.0
  google_fonts: ^6.2.1
  flutter_svg: ^2.1.0
  fl_chart: ^1.0.0
  
  # حزم الكاميرا والصور - مؤقتاً معطلة للبناء
  # image_picker: ^1.1.2
  # camera: ^0.11.1
  # image_cropper: ^8.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font.
  # Using google_fonts package instead of local font files
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
