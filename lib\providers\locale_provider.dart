import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleProvider extends ChangeNotifier {
  Locale _locale = const Locale('ar', 'DZ');
  bool _isRTL = true;

  LocaleProvider() {
    _loadLocale();
  }

  Locale get locale => _locale;
  bool get isRTL => _isRTL;

  Future<void> _loadLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final String languageCode = prefs.getString('languageCode') ?? 'ar';
    final String countryCode = prefs.getString('countryCode') ?? 'DZ';

    _locale = Locale(languageCode, countryCode);
    _isRTL = languageCode == 'ar';
    notifyListeners();
  }

  Future<void> setLocale(Locale locale) async {
    if (_locale == locale) return;

    _locale = locale;
    _isRTL = locale.languageCode == 'ar';

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('languageCode', locale.languageCode);
    await prefs.setString('countryCode', locale.countryCode ?? '');

    notifyListeners();
  }

  void toggleDirection() {
    if (_locale.languageCode == 'ar') {
      setLocale(const Locale('en', 'US'));
    } else if (_locale.languageCode == 'en') {
      setLocale(const Locale('fr', 'FR'));
    } else {
      setLocale(const Locale('ar', 'DZ'));
    }
  }

  void setLanguageFromCode(String languageCode) {
    switch (languageCode) {
      case 'ar':
        setLocale(const Locale('ar', 'DZ'));
        break;
      case 'en':
        setLocale(const Locale('en', 'US'));
        break;
      case 'fr':
        setLocale(const Locale('fr', 'FR'));
        break;
      default:
        setLocale(const Locale('ar', 'DZ'));
    }
  }
}
