import 'dart:io';
import 'dart:math';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:provider/provider.dart';
import 'package:flutter_barcode_scanner/flutter_barcode_scanner.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
// Temporarily removed image_cropper due to compatibility issues
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:pos_app/models/product.dart';
import 'package:pos_app/models/category.dart';
import 'package:pos_app/providers/product_provider.dart';
import 'package:pos_app/providers/category_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/widgets/category_widgets.dart';

class ProductFormScreen extends StatefulWidget {
  final Product? product;

  const ProductFormScreen({super.key, this.product});

  @override
  _ProductFormScreenState createState() => _ProductFormScreenState();
}

class _ProductFormScreenState extends State<ProductFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _barcodeController = TextEditingController();
  final _priceController = TextEditingController();
  final _costPriceController = TextEditingController();
  final _wholesalePriceController = TextEditingController();
  final _minWholesaleQtyController = TextEditingController();
  final _stockController = TextEditingController();
  final _categoryController = TextEditingController();
  final _imageUrlController = TextEditingController();

  final _imagePicker = ImagePicker();

  bool _enableWholesale = false;
  bool _hasExpiration = false;
  DateTime? _expirationDate;
  File? _imageFile;
  String? _selectedImageUrl;
  final bool _isLoading = false;
  bool _isSaving = false;
  bool _imagePickerVisible = false;
  bool _isEditing = false;
  Category? _selectedCategory;

  double _profitMargin = 0.0;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.product != null;

    if (_isEditing) {
      // Initialize controllers with existing product data
      _nameController.text = widget.product!.name;
      _barcodeController.text = widget.product!.barcode;
      _priceController.text = widget.product!.price.toString();
      _costPriceController.text = widget.product!.costPrice.toString();
      _wholesalePriceController.text =
          widget.product!.wholesalePrice.toString();
      _enableWholesale = widget.product!.enableWholesale;
      _minWholesaleQtyController.text =
          widget.product!.minWholesaleQty.toString();
      _stockController.text = widget.product!.stock.toString();
      _categoryController.text = widget.product!.category;
      
      // استرجاع الفئة المحددة إذا كانت موجودة
      final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
      if (widget.product!.category.isNotEmpty) {
        _selectedCategory = categoryProvider.getCategoryByName(widget.product!.category);
      }

      // Handle expiration date
      _hasExpiration = widget.product!.hasExpiration;
      _expirationDate = widget.product!.expirationDate;

      // Handle image
      if (widget.product!.imageUrl != null) {
        _imageUrlController.text = widget.product!.imageUrl!;
        _selectedImageUrl = widget.product!.imageUrl;
      }
    } else {
      // Set default values for new product
      _minWholesaleQtyController.text = '10';
      _stockController.text = '0';
      _costPriceController.text = '0.0';
      _priceController.text = '0.0';
      _wholesalePriceController.text = '0.0';
    }

    // Initial profit margin calculations
    _calculateProfitMargin();

    // Add listeners to update profit margins when values change
    _priceController.addListener(_calculateProfitMargin);
    _costPriceController.addListener(_calculateProfitMargin);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _barcodeController.dispose();
    _priceController.dispose();
    _costPriceController.dispose();
    _wholesalePriceController.dispose();
    _minWholesaleQtyController.dispose();
    _stockController.dispose();
    _categoryController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  // Calculate profit margin based on cost and retail price
  void _calculateProfitMargin() {
    try {
      final costPrice = double.tryParse(_costPriceController.text) ?? 0;
      final retailPrice = double.tryParse(_priceController.text) ?? 0;

      setState(() {
        if (costPrice > 0) {
          _profitMargin = (retailPrice - costPrice) / costPrice;
        } else {
          _profitMargin = 0;
        }
      });
    } catch (e) {
      // Ignore errors during calculation, they'll be handled during validation
    }
  }

  // Helper method to get color based on remaining days until expiration
  Color _getRemainingDaysColor(DateTime expirationDate) {
    final daysRemaining = expirationDate.difference(DateTime.now()).inDays;

    if (daysRemaining < 0) {
      return Colors.red; // Expired
    } else if (daysRemaining < 30) {
      return Colors.orange; // Less than a month left
    } else if (daysRemaining < 90) {
      return Colors.amber; // Less than 3 months left
    } else {
      return Colors.green; // More than 3 months left
    }
  }

  // Helper method to get text for remaining days until expiration
  String _getRemainingDaysText(DateTime expirationDate) {
    final daysRemaining = expirationDate.difference(DateTime.now()).inDays;

    if (daysRemaining < 0) {
      return 'Expired ${-daysRemaining} days ago';
    } else if (daysRemaining == 0) {
      return 'Expires today';
    } else if (daysRemaining == 1) {
      return 'Expires tomorrow';
    } else if (daysRemaining < 30) {
      return '$daysRemaining days left';
    } else if (daysRemaining < 365) {
      final months = (daysRemaining / 30).floor();
      return '$months ${months == 1 ? 'month' : 'months'} left';
    } else {
      final years = (daysRemaining / 365).floor();
      final remainingMonths = ((daysRemaining % 365) / 30).floor();
      return '$years ${years == 1 ? 'year' : 'years'}${remainingMonths > 0 ? ', $remainingMonths ${remainingMonths == 1 ? 'month' : 'months'}' : ''} left';
    }
  }

  // Generate a random barcode (EAN-13 format)
  void _generateBarcode() {
    final random = Random();
    // Generate first 12 digits (13th will be checksum)
    String barcode = '';
    for (int i = 0; i < 12; i++) {
      barcode += random.nextInt(10).toString();
    }

    // Calculate checksum digit (EAN-13)
    int sum = 0;
    for (int i = 0; i < 12; i++) {
      int digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit : digit * 3;
    }
    int checkDigit = (10 - (sum % 10)) % 10;
    barcode += checkDigit.toString();

    setState(() {
      _barcodeController.text = barcode;
    });
  }

  // Enhanced barcode scanning that opens camera directly
  Future<void> _scanBarcode() async {
    try {
      final barcode = await FlutterBarcodeScanner.scanBarcode(
        '#FF6750A4', // Use theme primary color
        'Cancel',
        true,
        ScanMode.BARCODE,
      );

      if (barcode != '-1') {
        setState(() {
          _barcodeController.text = barcode;
          // Show success feedback
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 10),
                  Text('Barcode scanned successfully'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        });
      }
    } on PlatformException {
      setState(() {
        // Show error feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                SizedBox(width: 10),
                Text('Failed to scan barcode'),
              ],
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      });
    }
  }

  // عرض نافذة إضافة فئة جديدة
  void _showAddCategoryDialog(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(isRTL ? 'إضافة فئة جديدة' : 'Add New Category'),
        content: SingleChildScrollView(
          child: CategoryForm(
            onSave: (newCategory) {
              final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
              categoryProvider.addCategory(newCategory).then((_) {
                // بعد إضافة الفئة، قم بتحديثها كفئة محددة
                setState(() {
                  _selectedCategory = categoryProvider.getCategoryByName(newCategory.name);
                  if (_selectedCategory != null) {
                    _categoryController.text = _selectedCategory!.name;
                  }
                });
                Navigator.of(ctx).pop();
                
                // عرض رسالة نجاح
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      isRTL ? 'تمت إضافة الفئة بنجاح' : 'Category added successfully',
                    ),
                    backgroundColor: Colors.green,
                  ),
                );
              });
            },
            onCancel: () => Navigator.of(ctx).pop(),
            isDialog: true,
          ),
        ),
        contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
        actionsPadding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  // Helper method to build image placeholder
  Widget _buildImagePlaceholder() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(FontAwesomeIcons.image, color: Colors.grey.shade400, size: 40),
          SizedBox(height: 8),
          Text(
            'Tap to add image',
            style: GoogleFonts.poppins(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build image source button
  Widget _buildImageSourceButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3), width: 1),
        ),
        child: Column(
          children: [
            FaIcon(icon, color: color, size: 24),
            SizedBox(height: 8),
            Text(
              label,
              style: GoogleFonts.poppins(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Pick image from gallery
  Future<void> _pickImageFromGallery() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        _cropImage(pickedFile);
      }
    } catch (e) {
      print('Error picking image from gallery: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _imagePickerVisible = false;
      });
    }
  }

  // Pick image from camera
  Future<void> _pickImageFromCamera() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        _cropImage(pickedFile);
      }
    } catch (e) {
      print('Error picking image from camera: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to capture image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _imagePickerVisible = false;
      });
    }
  }

  // Simplified image handling to avoid image_cropper issues
  Future<void> _cropImage(XFile pickedFile) async {
    try {
      final imagePath = pickedFile.path;

      if (kIsWeb) {
        setState(() {
          _selectedImageUrl = imagePath;
          _imageUrlController.text = imagePath;
        });
        return;
      }

      // Skip cropping for now to avoid compatibility issues
      setState(() {
        _imageFile = File(imagePath);
        _selectedImageUrl = imagePath;
        _imageUrlController.text = '';
      });
    } catch (e) {
      print('Error processing image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to process image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Save the image to app directory and return the file path
  Future<String?> _saveImageToAppDirectory() async {
    if (_imageFile == null) {
      return _selectedImageUrl;
    }

    try {
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = 'product_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final savedImage = await _imageFile!.copy('${appDir.path}/$fileName');
      return savedImage.path;
    } catch (e) {
      print('Error saving image: $e');
      return null;
    }
  }

  // Save product to database
  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // Save image if available
      final savedImagePath = await _saveImageToAppDirectory();

      // Get all form values
      final name = _nameController.text.trim();
      final barcode = _barcodeController.text.trim();
      final price = double.parse(_priceController.text);
      final costPrice = double.parse(_costPriceController.text);
      final wholesalePrice = double.parse(_wholesalePriceController.text);
      final minWholesaleQty = int.parse(_minWholesaleQtyController.text);
      final stock = int.parse(_stockController.text);
      final category = _selectedCategory != null ? _selectedCategory!.name : _categoryController.text.trim();
      final imageUrl =
          savedImagePath ??
          (_imageUrlController.text.isEmpty ? null : _imageUrlController.text);

      final productProvider = Provider.of<ProductProvider>(
        context,
        listen: false,
      );

      if (_isEditing) {
        // Update existing product
        final product = Product(
          id: widget.product!.id,
          name: name,
          barcode: barcode,
          price: price,
          costPrice: costPrice,
          wholesalePrice: wholesalePrice,
          enableWholesale: _enableWholesale,
          minWholesaleQty: minWholesaleQty,
          stock: stock,
          category: category,
          imageUrl: imageUrl,
          hasExpiration: _hasExpiration,
          expirationDate: _expirationDate,
        );

        await productProvider.updateProduct(product);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 10),
                  Text('Product updated successfully'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        // Create new product
        final product = Product(
          name: name,
          barcode: barcode,
          price: price,
          costPrice: costPrice,
          wholesalePrice: wholesalePrice,
          enableWholesale: _enableWholesale,
          minWholesaleQty: minWholesaleQty,
          stock: stock,
          category: category,
          imageUrl: imageUrl,
          hasExpiration: _hasExpiration,
          expirationDate: _expirationDate,
        );

        await productProvider.addProduct(product);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 10),
                  Text('Product added successfully'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      print('Error saving product: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                SizedBox(width: 10),
                Text('Error saving product: ${e.toString()}'),
              ],
            ),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final currencyFormatter = NumberFormat.currency(
      symbol: 'DA ',
      decimalDigits: 2,
    );
    final percentFormatter = NumberFormat.percentPattern();
    percentFormatter.maximumFractionDigits = 1;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditing ? 'Edit Product' : 'Add Product',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        actions: [
          if (_isEditing)
            IconButton(
              icon: Icon(Icons.delete, color: Colors.red),
              onPressed: () {
                // Show delete confirmation dialog
                showDialog(
                  context: context,
                  builder:
                      (context) => AlertDialog(
                        title: Text('Confirm Delete'),
                        content: Text(
                          'Are you sure you want to delete this product?',
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: Text('Cancel'),
                          ),
                          TextButton(
                            onPressed: () async {
                              Navigator.pop(context);
                              try {
                                final productProvider =
                                    Provider.of<ProductProvider>(
                                      context,
                                      listen: false,
                                    );
                                await productProvider.deleteProduct(
                                  widget.product!.id!,
                                );
                                if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Product deleted successfully',
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                  Navigator.pop(context);
                                }
                              } catch (e) {
                                if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Failed to delete product: $e',
                                      ),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              }
                            },
                            child: Text(
                              'Delete',
                              style: TextStyle(color: Colors.red),
                            ),
                          ),
                        ],
                      ),
                );
              },
            ),
        ],
      ),
      body:
          _isSaving
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Saving product...',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              )
              : Form(
                key: _formKey,
                child: Stack(
                  children: [
                    // Main Form
                    SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Image Selection Card
                          Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.image,
                                        color: colorScheme.primary,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Product Image',
                                        style: GoogleFonts.poppins(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: colorScheme.primary,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 16),
                                  Center(
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _imagePickerVisible =
                                              !_imagePickerVisible;
                                        });
                                      },
                                      child: Container(
                                        width: 200,
                                        height: 200,
                                        decoration: BoxDecoration(
                                          color: Colors.grey[200],
                                          borderRadius: BorderRadius.circular(
                                            10,
                                          ),
                                          border: Border.all(
                                            color: colorScheme.outline
                                                .withOpacity(0.5),
                                            width: 2,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withOpacity(
                                                0.1,
                                              ),
                                              blurRadius: 10,
                                              offset: Offset(0, 5),
                                            ),
                                          ],
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          child:
                                              _selectedImageUrl != null ||
                                                      _imageUrlController
                                                          .text
                                                          .isNotEmpty
                                                  ? kIsWeb
                                                      ? Image.network(
                                                        _selectedImageUrl ??
                                                            _imageUrlController
                                                                .text,
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (
                                                          context,
                                                          error,
                                                          stackTrace,
                                                        ) {
                                                          return _buildImagePlaceholder();
                                                        },
                                                      )
                                                      : _imageFile != null
                                                      ? Image.file(
                                                        _imageFile!,
                                                        fit: BoxFit.cover,
                                                      )
                                                      : _selectedImageUrl !=
                                                          null
                                                      ? Image.file(
                                                        File(
                                                          _selectedImageUrl!,
                                                        ),
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (
                                                          context,
                                                          error,
                                                          stackTrace,
                                                        ) {
                                                          return Image.network(
                                                            _selectedImageUrl!,
                                                            fit: BoxFit.cover,
                                                            errorBuilder: (
                                                              context,
                                                              error,
                                                              stackTrace,
                                                            ) {
                                                              return _buildImagePlaceholder();
                                                            },
                                                          );
                                                        },
                                                      )
                                                      : Image.network(
                                                        _imageUrlController
                                                            .text,
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (
                                                          context,
                                                          error,
                                                          stackTrace,
                                                        ) {
                                                          return _buildImagePlaceholder();
                                                        },
                                                      )
                                                  : _buildImagePlaceholder(),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(height: 16),
                                  if (_imagePickerVisible)
                                    Container(
                                      padding: EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: colorScheme.surfaceContainerHighest,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.stretch,
                                        children: [
                                          Text(
                                            'Add Product Image',
                                            style: GoogleFonts.poppins(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                          SizedBox(height: 8),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceEvenly,
                                            children: [
                                              _buildImageSourceButton(
                                                icon: FontAwesomeIcons.camera,
                                                label: 'Camera',
                                                onTap: _pickImageFromCamera,
                                                color: Colors.blue,
                                              ),
                                              _buildImageSourceButton(
                                                icon: FontAwesomeIcons.image,
                                                label: 'Gallery',
                                                onTap: _pickImageFromGallery,
                                                color: Colors.green,
                                              ),
                                              _buildImageSourceButton(
                                                icon: FontAwesomeIcons.xmark,
                                                label: 'Cancel',
                                                onTap: () {
                                                  setState(() {
                                                    _imagePickerVisible = false;
                                                  });
                                                },
                                                color: Colors.red,
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    )
                                  else
                                    TextFormField(
                                      controller: _imageUrlController,
                                      decoration: InputDecoration(
                                        labelText: 'Image URL',
                                        hintText:
                                            'Enter URL or tap image to upload',
                                        prefixIcon: Icon(Icons.link),
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),

                          SizedBox(height: 16),

                          // Basic Info Card
                          Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.circleInfo,
                                        color: colorScheme.primary,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Basic Information',
                                        style: GoogleFonts.poppins(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: colorScheme.primary,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 16),
                                  TextFormField(
                                    controller: _nameController,
                                    decoration: InputDecoration(
                                      labelText: 'Product Name*',
                                      hintText: 'Enter product name',
                                      prefixIcon: Icon(Icons.shopping_bag),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter product name';
                                      }
                                      return null;
                                    },
                                  ),
                                  SizedBox(height: 16),
                                  Consumer<CategoryProvider>(
                                    builder: (context, categoryProvider, _) {
                                      final categories = categoryProvider.categories;
                                      final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
                                      
                                      return Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          DropdownButtonFormField<Category?>(
                                            value: _selectedCategory,
                                            decoration: InputDecoration(
                                              labelText: isRTL ? 'الفئة*' : 'Category*',
                                              hintText: isRTL ? 'اختر فئة المنتج' : 'Select product category',
                                              prefixIcon: const Icon(Icons.category),
                                              border: OutlineInputBorder(
                                                borderRadius: BorderRadius.circular(12),
                                              ),
                                            ),
                                            items: [
                                              ...categories.map((category) => DropdownMenuItem<Category?>(
                                                value: category,
                                                child: Text(category.name),
                                              )),
                                              // إضافة خيار لإنشاء فئة جديدة
                                              DropdownMenuItem<Category?>(
                                                value: null,
                                                child: Row(
                                                  children: [
                                                    Icon(Icons.add, color: theme.colorScheme.primary, size: 18),
                                                    const SizedBox(width: 8),
                                                    Text(
                                                      isRTL ? 'إنشاء فئة جديدة...' : 'Create new category...',
                                                      style: TextStyle(
                                                        color: theme.colorScheme.primary,
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                            onChanged: (value) {
                                              setState(() {
                                                if (value == null && categories.isNotEmpty) {
                                                  // إذا اختار المستخدم "إنشاء فئة جديدة"
                                                  _showAddCategoryDialog(context);
                                                } else {
                                                  _selectedCategory = value;
                                                  if (value != null) {
                                                    _categoryController.text = value.name;
                                                  }
                                                }
                                              });
                                            },
                                            validator: (value) {
                                              if (_categoryController.text.isEmpty) {
                                                return isRTL ? 'الرجاء اختيار فئة' : 'Please select a category';
                                              }
                                              return null;
                                            },
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                  SizedBox(height: 16),
                                  TextFormField(
                                    controller: _stockController,
                                    decoration: InputDecoration(
                                      labelText: 'Stock Quantity*',
                                      hintText: 'Enter available quantity',
                                      prefixIcon: Icon(Icons.inventory),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    keyboardType: TextInputType.number,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter stock quantity';
                                      }
                                      return null;
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),

                          SizedBox(height: 16),

                          // Barcode Card
                          Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.barcode,
                                        color: colorScheme.primary,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Barcode',
                                        style: GoogleFonts.poppins(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: colorScheme.primary,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 16),
                                  TextFormField(
                                    controller: _barcodeController,
                                    decoration: InputDecoration(
                                      labelText: 'Barcode',
                                      hintText: 'Enter barcode or scan',
                                      prefixIcon: Icon(Icons.qr_code),
                                      suffixIcon: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          IconButton(
                                            icon: Icon(
                                              Icons.camera_alt,
                                              color: colorScheme.primary,
                                            ),
                                            onPressed: _scanBarcode,
                                            tooltip: 'Scan barcode',
                                          ),
                                          IconButton(
                                            icon: Icon(
                                              Icons.refresh,
                                              color: colorScheme.primary,
                                            ),
                                            onPressed: _generateBarcode,
                                            tooltip: 'Generate barcode',
                                          ),
                                        ],
                                      ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          SizedBox(height: 16),

                          // Pricing Card
                          Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.dollarSign,
                                        color: colorScheme.primary,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Pricing',
                                        style: GoogleFonts.poppins(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: colorScheme.primary,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 16),
                                  TextFormField(
                                    controller: _costPriceController,
                                    decoration: InputDecoration(
                                      labelText: 'Cost Price*',
                                      hintText: 'Enter cost price',
                                      prefixIcon: Icon(Icons.attach_money),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                          decimal: true,
                                        ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter cost price';
                                      }
                                      if (double.tryParse(value) == null) {
                                        return 'Please enter a valid number';
                                      }
                                      return null;
                                    },
                                  ),
                                  SizedBox(height: 16),
                                  TextFormField(
                                    controller: _priceController,
                                    decoration: InputDecoration(
                                      labelText: 'Retail Price*',
                                      hintText: 'Enter retail price',
                                      prefixIcon: Icon(Icons.sell),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                          decimal: true,
                                        ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter retail price';
                                      }
                                      if (double.tryParse(value) == null) {
                                        return 'Please enter a valid number';
                                      }
                                      return null;
                                    },
                                  ),
                                  SizedBox(height: 16),
                                  // Profit margin display
                                  Container(
                                    padding: EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color:
                                          _profitMargin >= 0
                                              ? Colors.green.withOpacity(0.1)
                                              : Colors.red.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color:
                                            _profitMargin >= 0
                                                ? Colors.green.withOpacity(0.3)
                                                : Colors.red.withOpacity(0.3),
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          _profitMargin >= 0
                                              ? Icons.trending_up
                                              : Icons.trending_down,
                                          color:
                                              _profitMargin >= 0
                                                  ? Colors.green
                                                  : Colors.red,
                                        ),
                                        SizedBox(width: 8),
                                        Text(
                                          'Profit Margin: ${percentFormatter.format(_profitMargin)}',
                                          style: GoogleFonts.poppins(
                                            fontWeight: FontWeight.w600,
                                            color:
                                                _profitMargin >= 0
                                                    ? Colors.green
                                                    : Colors.red,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          SizedBox(height: 16),

                          // Wholesale Card
                          Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.boxes,
                                        color: colorScheme.primary,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Wholesale',
                                        style: GoogleFonts.poppins(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: colorScheme.primary,
                                        ),
                                      ),
                                      Spacer(),
                                      Switch(
                                        value: _enableWholesale,
                                        onChanged: (value) {
                                          setState(() {
                                            _enableWholesale = value;
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                  if (_enableWholesale) ...[
                                    SizedBox(height: 16),
                                    TextFormField(
                                      controller: _wholesalePriceController,
                                      decoration: InputDecoration(
                                        labelText: 'Wholesale Price*',
                                        hintText: 'Enter wholesale price',
                                        prefixIcon: Icon(Icons.local_offer),
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                      ),
                                      keyboardType:
                                          TextInputType.numberWithOptions(
                                            decimal: true,
                                          ),
                                      validator:
                                          _enableWholesale
                                              ? (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return 'Please enter wholesale price';
                                                }
                                                if (double.tryParse(value) ==
                                                    null) {
                                                  return 'Please enter a valid number';
                                                }
                                                return null;
                                              }
                                              : null,
                                    ),
                                    SizedBox(height: 16),
                                    TextFormField(
                                      controller: _minWholesaleQtyController,
                                      decoration: InputDecoration(
                                        labelText:
                                            'Minimum Wholesale Quantity*',
                                        hintText:
                                            'Enter minimum quantity for wholesale',
                                        prefixIcon: Icon(
                                          Icons.format_list_numbered,
                                        ),
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                      ),
                                      keyboardType: TextInputType.number,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly,
                                      ],
                                      validator:
                                          _enableWholesale
                                              ? (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return 'Please enter minimum wholesale quantity';
                                                }
                                                return null;
                                              }
                                              : null,
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),

                          SizedBox(height: 16),

                          // Expiration Card
                          Card(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      FaIcon(
                                        FontAwesomeIcons.calendar,
                                        color: colorScheme.primary,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Expiration',
                                        style: GoogleFonts.poppins(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: colorScheme.primary,
                                        ),
                                      ),
                                      Spacer(),
                                      Switch(
                                        value: _hasExpiration,
                                        onChanged: (value) {
                                          setState(() {
                                            _hasExpiration = value;
                                            if (!value) {
                                              _expirationDate = null;
                                            }
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                  if (_hasExpiration) ...[
                                    SizedBox(height: 16),
                                    InkWell(
                                      onTap: () async {
                                        final date = await showDatePicker(
                                          context: context,
                                          initialDate:
                                              _expirationDate ??
                                              DateTime.now().add(
                                                Duration(days: 365),
                                              ),
                                          firstDate: DateTime.now(),
                                          lastDate: DateTime.now().add(
                                            Duration(days: 3650),
                                          ),
                                        );
                                        if (date != null) {
                                          setState(() {
                                            _expirationDate = date;
                                          });
                                        }
                                      },
                                      child: Container(
                                        padding: EdgeInsets.all(16),
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: colorScheme.outline,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            Icon(Icons.calendar_today),
                                            SizedBox(width: 12),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Expiration Date',
                                                    style: GoogleFonts.poppins(
                                                      fontSize: 12,
                                                      color: Colors.grey[600],
                                                    ),
                                                  ),
                                                  Text(
                                                    _expirationDate != null
                                                        ? DateFormat(
                                                          'MMM dd, yyyy',
                                                        ).format(
                                                          _expirationDate!,
                                                        )
                                                        : 'Select expiration date',
                                                    style: GoogleFonts.poppins(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            if (_expirationDate != null)
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                  horizontal: 8,
                                                  vertical: 4,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: _getRemainingDaysColor(
                                                    _expirationDate!,
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                ),
                                                child: Text(
                                                  _getRemainingDaysText(
                                                    _expirationDate!,
                                                  ),
                                                  style: GoogleFonts.poppins(
                                                    color: Colors.white,
                                                    fontSize: 10,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),

                          SizedBox(
                            height: 100,
                          ), // Extra space for floating action button
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _isSaving ? null : _saveProduct,
        icon:
            _isSaving
                ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
                : Icon(Icons.save),
        label: Text(
          _isSaving ? 'Saving...' : 'Save Product',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        backgroundColor: _isSaving ? Colors.grey : colorScheme.primary,
      ),
    );
  }
}
