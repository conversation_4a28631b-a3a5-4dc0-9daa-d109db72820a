import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

enum SupportedCurrency {
  dzd, // Algerian Dinar
  usd, // US Dollar
  eur, // Euro
  gbp, // British Pound
  sar, // Saudi Riyal
  aed, // UAE Dirham
  mad, // Moroccan Dirham
  tnd, // Tunisian Dinar
}

class CurrencyInfo {
  final String code;
  final String symbol;
  final String nameAr;
  final String nameEn;
  final String nameFr;
  final int decimalPlaces;
  final double exchangeRate; // Rate to USD

  const CurrencyInfo({
    required this.code,
    required this.symbol,
    required this.nameAr,
    required this.nameEn,
    required this.nameFr,
    required this.decimalPlaces,
    required this.exchangeRate,
  });
}

class CurrencyProvider extends ChangeNotifier {
  static const String _currencyKey = 'selected_currency';
  static const String _exchangeRatesKey = 'exchange_rates';
  static const String _lastUpdateKey = 'last_exchange_update';

  SupportedCurrency _currentCurrency = SupportedCurrency.dzd;
  Map<SupportedCurrency, double> _exchangeRates = {};
  DateTime? _lastExchangeUpdate;
  bool _isLoading = false;

  // Currency information map
  static const Map<SupportedCurrency, CurrencyInfo> _currencyInfo = {
    SupportedCurrency.dzd: CurrencyInfo(
      code: 'DZD',
      symbol: 'د.ج',
      nameAr: 'دينار جزائري',
      nameEn: 'Algerian Dinar',
      nameFr: 'Dinar Algérien',
      decimalPlaces: 2,
      exchangeRate: 134.0, // Example rate
    ),
    SupportedCurrency.usd: CurrencyInfo(
      code: 'USD',
      symbol: '\$',
      nameAr: 'دولار أمريكي',
      nameEn: 'US Dollar',
      nameFr: 'Dollar Américain',
      decimalPlaces: 2,
      exchangeRate: 1.0, // Base currency
    ),
    SupportedCurrency.eur: CurrencyInfo(
      code: 'EUR',
      symbol: '€',
      nameAr: 'يورو',
      nameEn: 'Euro',
      nameFr: 'Euro',
      decimalPlaces: 2,
      exchangeRate: 0.85, // Example rate
    ),
    SupportedCurrency.gbp: CurrencyInfo(
      code: 'GBP',
      symbol: '£',
      nameAr: 'جنيه إسترليني',
      nameEn: 'British Pound',
      nameFr: 'Livre Sterling',
      decimalPlaces: 2,
      exchangeRate: 0.73, // Example rate
    ),
    SupportedCurrency.sar: CurrencyInfo(
      code: 'SAR',
      symbol: 'ر.س',
      nameAr: 'ريال سعودي',
      nameEn: 'Saudi Riyal',
      nameFr: 'Riyal Saoudien',
      decimalPlaces: 2,
      exchangeRate: 3.75, // Example rate
    ),
    SupportedCurrency.aed: CurrencyInfo(
      code: 'AED',
      symbol: 'د.إ',
      nameAr: 'درهم إماراتي',
      nameEn: 'UAE Dirham',
      nameFr: 'Dirham des EAU',
      decimalPlaces: 2,
      exchangeRate: 3.67, // Example rate
    ),
    SupportedCurrency.mad: CurrencyInfo(
      code: 'MAD',
      symbol: 'د.م',
      nameAr: 'درهم مغربي',
      nameEn: 'Moroccan Dirham',
      nameFr: 'Dirham Marocain',
      decimalPlaces: 2,
      exchangeRate: 10.0, // Example rate
    ),
    SupportedCurrency.tnd: CurrencyInfo(
      code: 'TND',
      symbol: 'د.ت',
      nameAr: 'دينار تونسي',
      nameEn: 'Tunisian Dinar',
      nameFr: 'Dinar Tunisien',
      decimalPlaces: 3,
      exchangeRate: 3.1, // Example rate
    ),
  };

  // Getters
  SupportedCurrency get currentCurrency => _currentCurrency;
  CurrencyInfo get currentCurrencyInfo => _currencyInfo[_currentCurrency]!;
  bool get isLoading => _isLoading;
  DateTime? get lastExchangeUpdate => _lastExchangeUpdate;
  Map<SupportedCurrency, CurrencyInfo> get allCurrencies => _currencyInfo;

  CurrencyProvider() {
    _loadCurrency();
    _loadExchangeRates();
  }

  Future<void> _loadCurrency() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currencyString = prefs.getString(_currencyKey) ?? 'dzd';
      _currentCurrency = SupportedCurrency.values.firstWhere(
        (currency) => currency.name == currencyString,
        orElse: () => SupportedCurrency.dzd,
      );
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading currency: $e');
    }
  }

  Future<void> _loadExchangeRates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ratesJson = prefs.getString(_exchangeRatesKey);
      final lastUpdateString = prefs.getString(_lastUpdateKey);

      if (ratesJson != null) {
        // Load saved exchange rates
        // For now, we'll use default rates
        _initializeDefaultRates();
      } else {
        _initializeDefaultRates();
      }

      if (lastUpdateString != null) {
        _lastExchangeUpdate = DateTime.parse(lastUpdateString);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading exchange rates: $e');
      _initializeDefaultRates();
    }
  }

  void _initializeDefaultRates() {
    _exchangeRates = {
      for (var currency in SupportedCurrency.values)
        currency: _currencyInfo[currency]!.exchangeRate
    };
  }

  Future<void> setCurrency(SupportedCurrency currency) async {
    if (_currentCurrency == currency) return;

    try {
      _currentCurrency = currency;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_currencyKey, currency.name);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting currency: $e');
    }
  }

  String formatCurrency(double amount, {bool showSymbol = true, bool showCode = false}) {
    final info = currentCurrencyInfo;
    final formatter = NumberFormat.currency(
      symbol: showSymbol ? info.symbol : '',
      decimalDigits: info.decimalPlaces,
    );
    
    String formatted = formatter.format(amount);
    
    if (showCode && !showSymbol) {
      formatted = '$formatted ${info.code}';
    }
    
    return formatted;
  }

  String getCurrencyName(String languageCode) {
    final info = currentCurrencyInfo;
    switch (languageCode) {
      case 'ar':
        return info.nameAr;
      case 'fr':
        return info.nameFr;
      default:
        return info.nameEn;
    }
  }

  double convertAmount(double amount, SupportedCurrency fromCurrency, SupportedCurrency toCurrency) {
    if (fromCurrency == toCurrency) return amount;
    
    final fromRate = _exchangeRates[fromCurrency] ?? 1.0;
    final toRate = _exchangeRates[toCurrency] ?? 1.0;
    
    // Convert to USD first, then to target currency
    final usdAmount = amount / fromRate;
    return usdAmount * toRate;
  }

  Future<void> updateExchangeRates() async {
    _isLoading = true;
    notifyListeners();

    try {
      // In a real app, you would fetch from an API like:
      // - https://api.exchangerate-api.com/v4/latest/USD
      // - https://api.fixer.io/latest
      // For now, we'll simulate an update
      await Future.delayed(const Duration(seconds: 2));
      
      // Simulate rate updates (in real app, parse API response)
      _exchangeRates = {
        SupportedCurrency.dzd: 134.5,
        SupportedCurrency.usd: 1.0,
        SupportedCurrency.eur: 0.85,
        SupportedCurrency.gbp: 0.73,
        SupportedCurrency.sar: 3.75,
        SupportedCurrency.aed: 3.67,
        SupportedCurrency.mad: 10.1,
        SupportedCurrency.tnd: 3.1,
      };

      _lastExchangeUpdate = DateTime.now();
      
      // Save to preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastUpdateKey, _lastExchangeUpdate!.toIso8601String());
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating exchange rates: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  bool shouldUpdateRates() {
    if (_lastExchangeUpdate == null) return true;
    final now = DateTime.now();
    final difference = now.difference(_lastExchangeUpdate!);
    return difference.inHours >= 24; // Update daily
  }
}
