import 'dart:async';
import 'package:flutter/foundation.dart';
import '../db/database_helper.dart';
import '../providers/notification_provider.dart';
import '../models/product.dart';

class StockMonitoringService {
  static final StockMonitoringService _instance = StockMonitoringService._internal();
  factory StockMonitoringService() => _instance;
  StockMonitoringService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  NotificationProvider? _notificationProvider;
  Timer? _monitoringTimer;
  
  bool _isMonitoring = false;
  int _lowStockThreshold = 10;
  Duration _checkInterval = const Duration(minutes: 30);
  
  Set<int> _notifiedLowStockProducts = <int>{};
  Set<int> _notifiedOutOfStockProducts = <int>{};

  // Getters
  bool get isMonitoring => _isMonitoring;
  int get lowStockThreshold => _lowStockThreshold;
  Duration get checkInterval => _checkInterval;

  void initialize(NotificationProvider notificationProvider) {
    _notificationProvider = notificationProvider;
    _lowStockThreshold = notificationProvider.lowStockThreshold;
    
    // Listen to notification provider changes
    notificationProvider.addListener(_onNotificationSettingsChanged);
    
    // Start monitoring if notifications are enabled
    if (notificationProvider.lowStockEnabled) {
      startMonitoring();
    }
  }

  void _onNotificationSettingsChanged() {
    if (_notificationProvider == null) return;
    
    _lowStockThreshold = _notificationProvider!.lowStockThreshold;
    
    if (_notificationProvider!.lowStockEnabled && !_isMonitoring) {
      startMonitoring();
    } else if (!_notificationProvider!.lowStockEnabled && _isMonitoring) {
      stopMonitoring();
    }
  }

  void startMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    debugPrint('Stock monitoring started');
    
    // Check immediately
    _checkStockLevels();
    
    // Set up periodic checks
    _monitoringTimer = Timer.periodic(_checkInterval, (_) {
      _checkStockLevels();
    });
  }

  void stopMonitoring() {
    if (!_isMonitoring) return;
    
    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    
    debugPrint('Stock monitoring stopped');
  }

  void setCheckInterval(Duration interval) {
    _checkInterval = interval;
    
    if (_isMonitoring) {
      // Restart monitoring with new interval
      stopMonitoring();
      startMonitoring();
    }
  }

  Future<void> _checkStockLevels() async {
    if (_notificationProvider == null || !_notificationProvider!.lowStockEnabled) {
      return;
    }

    try {
      // Get low stock products
      final lowStockProducts = await _dbHelper.getLowStockProducts(_lowStockThreshold);
      final outOfStockProducts = await _dbHelper.getOutOfStockProducts();

      // Process low stock notifications
      for (final product in lowStockProducts) {
        if (product.quantity > 0 && !_notifiedLowStockProducts.contains(product.id)) {
          await _notificationProvider!.showLowStockAlert(
            product.name,
            product.quantity,
          );
          _notifiedLowStockProducts.add(product.id!);
          debugPrint('Low stock alert sent for: ${product.name}');
        }
      }

      // Process out of stock notifications
      for (final product in outOfStockProducts) {
        if (!_notifiedOutOfStockProducts.contains(product.id)) {
          await _notificationProvider!.showOutOfStockAlert(product.name);
          _notifiedOutOfStockProducts.add(product.id!);
          debugPrint('Out of stock alert sent for: ${product.name}');
        }
      }

      // Clean up notification tracking for products that are back in stock
      await _cleanupNotificationTracking();

    } catch (e) {
      debugPrint('Error checking stock levels: $e');
    }
  }

  Future<void> _cleanupNotificationTracking() async {
    try {
      final allProducts = await _dbHelper.getAllProducts();
      final currentProductIds = allProducts.map((p) => p.id!).toSet();
      
      // Remove tracking for deleted products
      _notifiedLowStockProducts.removeWhere((id) => !currentProductIds.contains(id));
      _notifiedOutOfStockProducts.removeWhere((id) => !currentProductIds.contains(id));
      
      // Remove tracking for products that are back in good stock
      for (final product in allProducts) {
        if (product.quantity > _lowStockThreshold) {
          _notifiedLowStockProducts.remove(product.id);
        }
        if (product.quantity > 0) {
          _notifiedOutOfStockProducts.remove(product.id);
        }
      }
    } catch (e) {
      debugPrint('Error cleaning up notification tracking: $e');
    }
  }

  // Manual check method that can be called after stock updates
  Future<void> checkStockAfterUpdate(Product product) async {
    if (_notificationProvider == null || !_notificationProvider!.lowStockEnabled) {
      return;
    }

    try {
      final productId = product.id!;
      
      if (product.quantity == 0) {
        // Product is out of stock
        if (!_notifiedOutOfStockProducts.contains(productId)) {
          await _notificationProvider!.showOutOfStockAlert(product.name);
          _notifiedOutOfStockProducts.add(productId);
          debugPrint('Out of stock alert sent for: ${product.name}');
        }
        // Remove from low stock tracking since it's now out of stock
        _notifiedLowStockProducts.remove(productId);
        
      } else if (product.quantity <= _lowStockThreshold) {
        // Product is low in stock
        if (!_notifiedLowStockProducts.contains(productId)) {
          await _notificationProvider!.showLowStockAlert(
            product.name,
            product.quantity,
          );
          _notifiedLowStockProducts.add(productId);
          debugPrint('Low stock alert sent for: ${product.name}');
        }
        // Remove from out of stock tracking since it has some stock
        _notifiedOutOfStockProducts.remove(productId);
        
      } else {
        // Product has good stock level
        _notifiedLowStockProducts.remove(productId);
        _notifiedOutOfStockProducts.remove(productId);
      }
    } catch (e) {
      debugPrint('Error checking stock after update: $e');
    }
  }

  // Method to check multiple products at once (useful after bulk updates)
  Future<void> checkStockAfterBulkUpdate(List<Product> products) async {
    for (final product in products) {
      await checkStockAfterUpdate(product);
    }
  }

  // Reset notification tracking (useful when changing thresholds)
  void resetNotificationTracking() {
    _notifiedLowStockProducts.clear();
    _notifiedOutOfStockProducts.clear();
    debugPrint('Notification tracking reset');
  }

  // Get current stock status summary
  Future<Map<String, dynamic>> getStockStatusSummary() async {
    try {
      final lowStockProducts = await _dbHelper.getLowStockProducts(_lowStockThreshold);
      final outOfStockProducts = await _dbHelper.getOutOfStockProducts();
      final allProducts = await _dbHelper.getAllProducts();
      
      final totalProducts = allProducts.length;
      final lowStockCount = lowStockProducts.where((p) => p.quantity > 0).length;
      final outOfStockCount = outOfStockProducts.length;
      final goodStockCount = totalProducts - lowStockCount - outOfStockCount;
      
      return {
        'totalProducts': totalProducts,
        'goodStock': goodStockCount,
        'lowStock': lowStockCount,
        'outOfStock': outOfStockCount,
        'lowStockProducts': lowStockProducts.where((p) => p.quantity > 0).toList(),
        'outOfStockProducts': outOfStockProducts,
      };
    } catch (e) {
      debugPrint('Error getting stock status summary: $e');
      return {
        'totalProducts': 0,
        'goodStock': 0,
        'lowStock': 0,
        'outOfStock': 0,
        'lowStockProducts': <Product>[],
        'outOfStockProducts': <Product>[],
      };
    }
  }

  // Dispose method to clean up resources
  void dispose() {
    stopMonitoring();
    _notificationProvider?.removeListener(_onNotificationSettingsChanged);
    _notificationProvider = null;
  }
}
