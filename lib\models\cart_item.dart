import 'package:pos_app/models/product.dart';

class CartItem {
  final int? id;
  final Product product;
  int quantity;
  final double price;

  CartItem({
    this.id,
    required this.product,
    required this.quantity,
    required this.price,
  });

  double get total => price * quantity;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': product.id,
      'quantity': quantity,
      'price': price,
    };
  }

  factory CartItem.fromMap(Map<String, dynamic> map, Product product) {
    return CartItem(
      id: map['id'],
      product: product,
      quantity: map['quantity'],
      price: map['price'],
    );
  }

  CartItem copyWith({
    int? id,
    Product? product,
    int? quantity,
    double? price,
  }) {
    return CartItem(
      id: id ?? this.id,
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
    );
  }
}
