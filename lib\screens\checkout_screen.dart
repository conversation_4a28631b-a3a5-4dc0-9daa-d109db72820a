import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'dart:ui' as ui;
import 'package:pos_app/providers/cart_provider.dart';
import 'package:pos_app/providers/customer_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/models/customer.dart';
import 'package:pos_app/screens/receipt_screen.dart';
import 'package:pos_app/models/invoice.dart';
import 'package:pos_app/db/database_bridge.dart';

class CheckoutScreen extends StatefulWidget {
  const CheckoutScreen({super.key});

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _customerNameController = TextEditingController();
  String _paymentMethod = 'Cash';
  double _amountPaid = 0.0;
  double _change = 0.0;
  Customer? _selectedCustomer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cartProvider = Provider.of<CartProvider>(context, listen: false);
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );

      _customerNameController.text = cartProvider.customerName;
      _paymentMethod = cartProvider.paymentMethod;

      // Load customers
      customerProvider.loadCustomers();
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _customerNameController.dispose();
    super.dispose();
  }

  void _calculateChange() {
    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    final totalAmount = cartProvider.totalAmount;
    setState(() {
      _amountPaid = double.tryParse(_amountController.text) ?? 0.0;
      _change = _amountPaid - totalAmount;
    });
  }

  void _completeCheckout() async {
    if (!_formKey.currentState!.validate()) return;

    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    cartProvider.customerName = _customerNameController.text;
    cartProvider.paymentMethod = _paymentMethod;

    try {
      // إتمام عملية الدفع
      final transaction = await cartProvider.checkout(_amountPaid);

      if (!mounted) return;

      // استخدام جسر قاعدة البيانات
      final dbBridge = DatabaseBridge();

      // إنشاء رقم الفاتورة بشكل بسيط
      final String invoicePrefix = 'INV';
      final int invoiceNumber = DateTime.now().millisecondsSinceEpoch % 10000;
      final String formattedInvoiceNumber = '$invoicePrefix$invoiceNumber';

      // إنشاء كائن الفاتورة
      final invoice = Invoice(
        id: 0, // سيتم تعيينه في قاعدة البيانات
        invoiceNumber: formattedInvoiceNumber,
        date: DateTime.now(),
        customerId: _selectedCustomer?.id,
        customerName: _customerNameController.text,
        totalAmount: transaction.total,
        discountAmount: 0.0, // transaction.discount,
        taxAmount: 0.0, // transaction.tax,
        finalAmount: transaction.total,
        paymentMethod: _paymentMethod,
        status: 'completed',
        notes: '',
        items:
            cartProvider.items
                .map(
                  (cartItem) => InvoiceItem(
                    productId: cartItem.product.id!,
                    productName: cartItem.product.name,
                    quantity: cartItem.quantity.toDouble(),
                    unitPrice: cartItem.price,
                    totalPrice: cartItem.price * cartItem.quantity,
                  ),
                )
                .toList(),
        // itemsJson تم إزالة هذا المعامل من النموذج
      );

      // إضافة الفاتورة إلى قاعدة البيانات
      try {
        final invoiceId = await dbBridge.addInvoice(invoice);
        debugPrint('تم حفظ الفاتورة برقم معرّف: $invoiceId');

        // زيادة رقم الفاتورة التالي
        await dbBridge.incrementNextInvoiceNumber();

        // تم إنشاء الفاتورة بنجاح مع ID: $invoiceId
      } catch (e) {
        debugPrint('خطأ في حفظ الفاتورة: $e');
        // استمر على أي حال حتى لو فشل حفظ الفاتورة
      }

      // تحديد نوع شاشة العرض بناءً على إعدادات التطبيق
      if (!mounted) return;

      // عرض إيصال بسيط
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => ReceiptScreen(transaction: transaction),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      final localeProvider = Provider.of<LocaleProvider>(
        context,
        listen: false,
      );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            localeProvider.isRTL
                ? 'فشلت عملية الدفع: $e'
                : 'Checkout failed: $e',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<CartProvider, CustomerProvider, LocaleProvider>(
      builder: (context, cartProvider, customerProvider, localeProvider, _) {
        final formatter = NumberFormat.currency(
          symbol: localeProvider.isRTL ? 'د.ج ' : 'DZD ',
          decimalDigits: 2,
        );
        final totalAmount = cartProvider.totalAmount;

        return Scaffold(
          appBar: AppBar(
            title: Text(localeProvider.isRTL ? 'الدفع' : 'Checkout'),
          ),
          body: Form(
            key: _formKey,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Total Amount Section with Green Background
                  Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    padding: const EdgeInsets.all(20.0),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.green.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          localeProvider.isRTL
                              ? 'المجموع الإجمالي: ${formatter.format(totalAmount)}'
                              : 'Total: ${formatter.format(totalAmount)}',
                          style: Theme.of(
                            context,
                          ).textTheme.headlineMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                          textDirection:
                              localeProvider.isRTL
                                  ? ui.TextDirection.rtl
                                  : ui.TextDirection.ltr,
                        ),
                        if (_amountPaid > 0) ...[
                          const SizedBox(height: 12),
                          Text(
                            localeProvider.isRTL
                                ? 'المبلغ المدفوع: ${formatter.format(_amountPaid)}'
                                : 'Amount Paid: ${formatter.format(_amountPaid)}',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(color: Colors.white),
                            textDirection:
                                localeProvider.isRTL
                                    ? ui.TextDirection.rtl
                                    : ui.TextDirection.ltr,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            localeProvider.isRTL
                                ? 'الباقي: ${formatter.format(_change)}'
                                : 'Change: ${formatter.format(_change)}',
                            style: Theme.of(
                              context,
                            ).textTheme.titleMedium?.copyWith(
                              color:
                                  _change >= 0
                                      ? Colors.white
                                      : Colors.red.shade100,
                              fontWeight: FontWeight.bold,
                            ),
                            textDirection:
                                localeProvider.isRTL
                                    ? ui.TextDirection.rtl
                                    : ui.TextDirection.ltr,
                          ),
                        ],
                      ],
                    ),
                  ),
                  // Customer Selection Dropdown
                  DropdownButtonFormField<Customer?>(
                    value: _selectedCustomer,
                    decoration: InputDecoration(
                      labelText: localeProvider.isRTL ? 'العميل' : 'Customer',
                      prefixIcon: const Icon(Icons.person),
                      border: const OutlineInputBorder(),
                    ),
                    items: [
                      DropdownMenuItem<Customer?>(
                        value: null,
                        child: Text(
                          localeProvider.isRTL ? 'عميل نقدي' : 'Cash Customer',
                          textDirection:
                              localeProvider.isRTL
                                  ? ui.TextDirection.rtl
                                  : ui.TextDirection.ltr,
                        ),
                      ),
                      ...customerProvider.customers.map(
                        (customer) => DropdownMenuItem<Customer?>(
                          value: customer,
                          child: Text(
                            customer.name,
                            textDirection:
                                localeProvider.isRTL
                                    ? ui.TextDirection.rtl
                                    : ui.TextDirection.ltr,
                          ),
                        ),
                      ),
                    ],
                    onChanged: (Customer? customer) {
                      setState(() {
                        _selectedCustomer = customer;
                        _customerNameController.text = customer?.name ?? '';
                        cartProvider.customerName = customer?.name ?? '';
                      });
                    },
                  ),

                  // Customer Debt Balance Display
                  if (_selectedCustomer != null) ...[
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color:
                            _selectedCustomer!.balance > 0
                                ? Colors.orange.shade50
                                : Colors.green.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color:
                              _selectedCustomer!.balance > 0
                                  ? Colors.orange
                                  : Colors.green,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            _selectedCustomer!.balance > 0
                                ? Icons.warning_amber
                                : Icons.check_circle,
                            color:
                                _selectedCustomer!.balance > 0
                                    ? Colors.orange
                                    : Colors.green,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              localeProvider.isRTL
                                  ? 'الرصيد: ${formatter.format(_selectedCustomer!.balance)}'
                                  : 'Balance: ${formatter.format(_selectedCustomer!.balance)}',
                              style: TextStyle(
                                color:
                                    _selectedCustomer!.balance > 0
                                        ? Colors.orange.shade700
                                        : Colors.green.shade700,
                                fontWeight: FontWeight.w600,
                              ),
                              textDirection:
                                  localeProvider.isRTL
                                      ? ui.TextDirection.rtl
                                      : ui.TextDirection.ltr,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _paymentMethod,
                    decoration: const InputDecoration(
                      labelText: 'Payment Method',
                      prefixIcon: Icon(Icons.payment),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'Cash', child: Text('Cash')),
                      DropdownMenuItem(value: 'Card', child: Text('Card')),
                      DropdownMenuItem(
                        value: 'Mobile Payment',
                        child: Text('Mobile Payment'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _paymentMethod = value!;
                        cartProvider.paymentMethod = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _amountController,
                    decoration: const InputDecoration(
                      labelText: 'Amount Paid',
                      prefixIcon: Icon(Icons.attach_money),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter the amount';
                      }
                      final amount = double.tryParse(value);
                      if (amount == null) {
                        return 'Please enter a valid amount';
                      }
                      if (amount < totalAmount) {
                        return 'Amount must be at least the total';
                      }
                      return null;
                    },
                    onChanged: (_) => _calculateChange(),
                  ),
                  const Spacer(),

                  // Invoice Action Buttons
                  if (cartProvider.itemCount > 0) ...[
                    Text(
                      localeProvider.isRTL
                          ? 'إجراءات الفاتورة'
                          : 'Invoice Actions',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        // Preview Invoice Button
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed:
                                () => _previewInvoice(context, localeProvider),
                            icon: const Icon(Icons.preview),
                            label: Text(
                              localeProvider.isRTL ? 'معاينة' : 'Preview',
                              style: const TextStyle(fontSize: 12),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Print Invoice Button
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed:
                                () => _printInvoice(context, localeProvider),
                            icon: const Icon(Icons.print),
                            label: Text(
                              localeProvider.isRTL ? 'طباعة' : 'Print',
                              style: const TextStyle(fontSize: 12),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        // Share via WhatsApp Button
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed:
                                () =>
                                    _shareViaWhatsApp(context, localeProvider),
                            icon: const Icon(
                              Icons.message,
                              color: Colors.green,
                            ),
                            label: Text(
                              'WhatsApp',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.green,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: const BorderSide(color: Colors.green),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Share via Email Button
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed:
                                () => _shareViaEmail(context, localeProvider),
                            icon: const Icon(Icons.email, color: Colors.blue),
                            label: Text(
                              localeProvider.isRTL ? 'إيميل' : 'Email',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.blue,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: const BorderSide(color: Colors.blue),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                  ],

                  // Complete Sale Button
                  ElevatedButton(
                    onPressed:
                        _amountPaid >= totalAmount ? _completeCheckout : null,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: Text(
                      localeProvider.isRTL ? 'إتمام البيع' : 'Complete Sale',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _previewInvoice(BuildContext context, LocaleProvider localeProvider) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              localeProvider.isRTL ? 'معاينة الفاتورة' : 'Invoice Preview',
            ),
            content: Text(
              localeProvider.isRTL
                  ? 'ستتم إضافة معاينة الفاتورة قريباً'
                  : 'Invoice preview will be added soon',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localeProvider.isRTL ? 'إغلاق' : 'Close'),
              ),
            ],
          ),
    );
  }

  void _printInvoice(BuildContext context, LocaleProvider localeProvider) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          localeProvider.isRTL
              ? 'ستتم إضافة ميزة الطباعة قريباً'
              : 'Print feature will be added soon',
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _shareViaWhatsApp(BuildContext context, LocaleProvider localeProvider) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          localeProvider.isRTL
              ? 'ستتم إضافة مشاركة WhatsApp قريباً'
              : 'WhatsApp sharing will be added soon',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareViaEmail(BuildContext context, LocaleProvider localeProvider) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          localeProvider.isRTL
              ? 'ستتم إضافة مشاركة الإيميل قريباً'
              : 'Email sharing will be added soon',
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
