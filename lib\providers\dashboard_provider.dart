import 'package:flutter/foundation.dart';
import 'package:pos_app/db/database_helper.dart';
import 'package:pos_app/models/invoice.dart';

class DashboardStats {
  final int totalCustomers;
  final double dailySales;
  final double monthlySales;
  final int totalProducts;
  final int totalInvoices;
  final double totalProfits;
  final int lowStockProducts;
  final List<Invoice> recentInvoices;

  DashboardStats({
    required this.totalCustomers,
    required this.dailySales,
    required this.monthlySales,
    required this.totalProducts,
    required this.totalInvoices,
    required this.totalProfits,
    required this.lowStockProducts,
    required this.recentInvoices,
  });
}

class DashboardProvider extends ChangeNotifier {
  DashboardStats? _stats;
  bool _isLoading = false;
  String? _error;

  DashboardStats? get stats => _stats;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadDashboardData() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Get current date ranges
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0, 23, 59, 59);

      // Load all data in parallel
      final results = await Future.wait([
        _getTotalCustomers(),
        _getDailySales(startOfDay, endOfDay),
        _getMonthlySales(startOfMonth, endOfMonth),
        _getTotalProducts(),
        _getTotalInvoices(),
        _getTotalProfits(),
        _getLowStockProducts(),
        _getRecentInvoices(),
      ]);

      _stats = DashboardStats(
        totalCustomers: results[0] as int,
        dailySales: results[1] as double,
        monthlySales: results[2] as double,
        totalProducts: results[3] as int,
        totalInvoices: results[4] as int,
        totalProfits: results[5] as double,
        lowStockProducts: results[6] as int,
        recentInvoices: results[7] as List<Invoice>,
      );

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      debugPrint('Error loading dashboard data: $e');
    }
  }

  Future<int> _getTotalCustomers() async {
    try {
      final db = await DatabaseHelper.instance.database;
      final result = await db.query('customers');
      return result.length;
    } catch (e) {
      debugPrint('Error getting total customers: $e');
      return 0;
    }
  }

  Future<double> _getDailySales(DateTime start, DateTime end) async {
    try {
      final invoices = await DatabaseHelper.instance.getInvoicesByDateRange(
        start,
        end,
      );
      return invoices.fold<double>(
        0.0,
        (sum, invoice) => sum + invoice.finalAmount,
      );
    } catch (e) {
      debugPrint('Error getting daily sales: $e');
      return 0.0;
    }
  }

  Future<double> _getMonthlySales(DateTime start, DateTime end) async {
    try {
      final invoices = await DatabaseHelper.instance.getInvoicesByDateRange(
        start,
        end,
      );
      return invoices.fold<double>(
        0.0,
        (sum, invoice) => sum + invoice.finalAmount,
      );
    } catch (e) {
      debugPrint('Error getting monthly sales: $e');
      return 0.0;
    }
  }

  Future<int> _getTotalProducts() async {
    try {
      final products = await DatabaseHelper.instance.getAllProducts();
      return products.length;
    } catch (e) {
      debugPrint('Error getting total products: $e');
      return 0;
    }
  }

  Future<int> _getTotalInvoices() async {
    try {
      final invoices = await DatabaseHelper.instance.getAllInvoices();
      return invoices.length;
    } catch (e) {
      debugPrint('Error getting total invoices: $e');
      return 0;
    }
  }

  Future<double> _getTotalProfits() async {
    try {
      final invoices = await DatabaseHelper.instance.getAllInvoices();
      // Simple calculation: assume 20% profit margin
      double totalProfits = 0.0;
      for (var invoice in invoices) {
        totalProfits += invoice.finalAmount * 0.2; // 20% profit margin
      }
      return totalProfits;
    } catch (e) {
      debugPrint('Error calculating total profits: $e');
      return 0.0;
    }
  }

  Future<int> _getLowStockProducts() async {
    try {
      final products = await DatabaseHelper.instance.getAllProducts();
      return products.where((product) => product.stock <= 10).length;
    } catch (e) {
      debugPrint('Error getting low stock products: $e');
      return 0;
    }
  }

  Future<List<Invoice>> _getRecentInvoices() async {
    try {
      final allInvoices = await DatabaseHelper.instance.getAllInvoices();
      // Sort by date descending and take first 10
      allInvoices.sort((a, b) => b.date.compareTo(a.date));
      return allInvoices.take(10).toList();
    } catch (e) {
      debugPrint('Error getting recent invoices: $e');
      return [];
    }
  }

  void refreshData() {
    loadDashboardData();
  }

  // Method to update stats when new data is added
  void updateStats() {
    if (_stats != null) {
      loadDashboardData();
    }
  }
}
