import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/database_helper.dart';

enum BackupType { manual, daily, weekly, monthly }

enum BackupStatus {
  idle,
  creating,
  uploading,
  downloading,
  restoring,
  completed,
  failed,
}

class BackupInfo {
  final String id;
  final String name;
  final String path;
  final int size;
  final DateTime createdAt;
  final BackupType type;
  final BackupStatus status;
  final bool isEncrypted;
  final bool isCloudBackup;

  BackupInfo({
    required this.id,
    required this.name,
    required this.path,
    required this.size,
    required this.createdAt,
    required this.type,
    this.status = BackupStatus.completed,
    this.isEncrypted = false,
    this.isCloudBackup = false,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'path': path,
    'size': size,
    'createdAt': createdAt.toIso8601String(),
    'type': type.name,
    'status': status.name,
    'isEncrypted': isEncrypted,
    'isCloudBackup': isCloudBackup,
  };

  factory BackupInfo.fromJson(Map<String, dynamic> json) => BackupInfo(
    id: json['id'],
    name: json['name'],
    path: json['path'],
    size: json['size'],
    createdAt: DateTime.parse(json['createdAt']),
    type: BackupType.values.firstWhere((e) => e.name == json['type']),
    status: BackupStatus.values.firstWhere((e) => e.name == json['status']),
    isEncrypted: json['isEncrypted'] ?? false,
    isCloudBackup: json['isCloudBackup'] ?? false,
  );
}

class BackupProvider extends ChangeNotifier {
  static const String _backupHistoryKey = 'backup_history';
  static const String _backupScheduleKey = 'backup_schedule';
  static const String _lastBackupKey = 'last_backup';

  final DatabaseHelper _dbHelper = DatabaseHelper();

  BackupStatus _status = BackupStatus.idle;
  double _progress = 0.0;
  String? _errorMessage;
  List<BackupInfo> _backupHistory = [];
  BackupType _scheduleType = BackupType.manual;
  DateTime? _lastBackupDate;
  bool _isEncryptionEnabled = true;
  bool _isCloudBackupEnabled = false;

  // Getters
  BackupStatus get status => _status;
  double get progress => _progress;
  String? get errorMessage => _errorMessage;
  List<BackupInfo> get backupHistory => _backupHistory;
  BackupType get scheduleType => _scheduleType;
  DateTime? get lastBackupDate => _lastBackupDate;
  bool get isEncryptionEnabled => _isEncryptionEnabled;
  bool get isCloudBackupEnabled => _isCloudBackupEnabled;
  bool get isOperationInProgress =>
      _status != BackupStatus.idle &&
      _status != BackupStatus.completed &&
      _status != BackupStatus.failed;

  BackupProvider() {
    _loadSettings();
    _loadBackupHistory();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final scheduleString = prefs.getString(_backupScheduleKey) ?? 'manual';
      _scheduleType = BackupType.values.firstWhere(
        (e) => e.name == scheduleString,
        orElse: () => BackupType.manual,
      );

      final lastBackupString = prefs.getString(_lastBackupKey);
      if (lastBackupString != null) {
        _lastBackupDate = DateTime.parse(lastBackupString);
      }

      _isEncryptionEnabled = prefs.getBool('backup_encryption') ?? true;
      _isCloudBackupEnabled = prefs.getBool('cloud_backup_enabled') ?? false;

      notifyListeners();
    } catch (e) {
      debugPrint('Error loading backup settings: $e');
    }
  }

  Future<void> _loadBackupHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_backupHistoryKey);
      if (historyJson != null) {
        final List<dynamic> historyList = jsonDecode(historyJson);
        _backupHistory =
            historyList.map((json) => BackupInfo.fromJson(json)).toList();
        _backupHistory.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading backup history: $e');
    }
  }

  Future<void> _saveBackupHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = jsonEncode(
        _backupHistory.map((backup) => backup.toJson()).toList(),
      );
      await prefs.setString(_backupHistoryKey, historyJson);
    } catch (e) {
      debugPrint('Error saving backup history: $e');
    }
  }

  Future<void> setBackupSchedule(BackupType type) async {
    try {
      _scheduleType = type;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_backupScheduleKey, type.name);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting backup schedule: $e');
    }
  }

  Future<void> setEncryptionEnabled(bool enabled) async {
    try {
      _isEncryptionEnabled = enabled;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('backup_encryption', enabled);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting encryption preference: $e');
    }
  }

  Future<void> setCloudBackupEnabled(bool enabled) async {
    try {
      _isCloudBackupEnabled = enabled;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('cloud_backup_enabled', enabled);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting cloud backup preference: $e');
    }
  }

  String _encryptData(String data, String password) {
    final key = utf8.encode(password.padRight(32, '0').substring(0, 32));
    final bytes = utf8.encode(data);

    // Simple XOR encryption for demo purposes
    // In production, use proper encryption like AES
    final encrypted = <int>[];
    for (int i = 0; i < bytes.length; i++) {
      encrypted.add(bytes[i] ^ key[i % key.length]);
    }

    return base64.encode(encrypted);
  }

  String _decryptData(String encryptedData, String password) {
    final key = utf8.encode(password.padRight(32, '0').substring(0, 32));
    final encrypted = base64.decode(encryptedData);

    final decrypted = <int>[];
    for (int i = 0; i < encrypted.length; i++) {
      decrypted.add(encrypted[i] ^ key[i % key.length]);
    }

    return utf8.decode(decrypted);
  }

  Future<bool> createBackup({
    String? customName,
    BackupType type = BackupType.manual,
    String? encryptionPassword,
  }) async {
    if (isOperationInProgress) return false;

    try {
      _status = BackupStatus.creating;
      _progress = 0.0;
      _errorMessage = null;
      notifyListeners();

      // Export database
      _progress = 0.2;
      notifyListeners();

      final backupData = await _dbHelper.exportToJson();

      _progress = 0.4;
      notifyListeners();

      // Generate backup name
      final timestamp = DateTime.now();
      final backupName =
          customName ??
          'backup_${timestamp.year}${timestamp.month.toString().padLeft(2, '0')}${timestamp.day.toString().padLeft(2, '0')}_${timestamp.hour.toString().padLeft(2, '0')}${timestamp.minute.toString().padLeft(2, '0')}';

      String jsonData = jsonEncode(backupData);

      // Encrypt if enabled
      bool isEncrypted = false;
      if (_isEncryptionEnabled &&
          encryptionPassword != null &&
          encryptionPassword.isNotEmpty) {
        jsonData = _encryptData(jsonData, encryptionPassword);
        isEncrypted = true;
      }

      _progress = 0.6;
      notifyListeners();

      String backupPath;
      int fileSize;

      if (kIsWeb) {
        // For web, we'll simulate file creation
        backupPath = 'web_backup_$backupName.json';
        fileSize = jsonData.length;
      } else {
        // Save to device storage
        final directory = await getApplicationDocumentsDirectory();
        final backupsDir = Directory('${directory.path}/backups');
        if (!await backupsDir.exists()) {
          await backupsDir.create(recursive: true);
        }

        final file = File('${backupsDir.path}/$backupName.json');
        await file.writeAsString(jsonData);
        backupPath = file.path;
        fileSize = await file.length();
      }

      _progress = 0.8;
      notifyListeners();

      // Create backup info
      final backupInfo = BackupInfo(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: backupName,
        path: backupPath,
        size: fileSize,
        createdAt: timestamp,
        type: type,
        status: BackupStatus.completed,
        isEncrypted: isEncrypted,
        isCloudBackup: false,
      );

      // Add to history
      _backupHistory.insert(0, backupInfo);
      await _saveBackupHistory();

      // Update last backup date
      _lastBackupDate = timestamp;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastBackupKey, timestamp.toIso8601String());

      _progress = 1.0;
      _status = BackupStatus.completed;
      notifyListeners();

      return true;
    } catch (e) {
      _status = BackupStatus.failed;
      _errorMessage = 'Backup failed: $e';
      notifyListeners();
      return false;
    }
  }

  Future<bool> restoreBackup(
    BackupInfo backupInfo, {
    String? decryptionPassword,
  }) async {
    if (isOperationInProgress) return false;

    try {
      _status = BackupStatus.restoring;
      _progress = 0.0;
      _errorMessage = null;
      notifyListeners();

      String jsonData;

      if (kIsWeb) {
        // For web, we would need to implement file reading differently
        // For now, return false as web restore is complex
        throw Exception('Restore from web storage not implemented');
      } else {
        // Read from device storage
        final file = File(backupInfo.path);
        if (!await file.exists()) {
          throw Exception('Backup file not found');
        }
        jsonData = await file.readAsString();
      }

      _progress = 0.3;
      notifyListeners();

      // Decrypt if needed
      if (backupInfo.isEncrypted) {
        if (decryptionPassword == null || decryptionPassword.isEmpty) {
          throw Exception('Decryption password required');
        }
        jsonData = _decryptData(jsonData, decryptionPassword);
      }

      _progress = 0.5;
      notifyListeners();

      // Parse backup data
      final backupData = jsonDecode(jsonData) as Map<String, dynamic>;

      _progress = 0.7;
      notifyListeners();

      // Restore to database
      final success = await _dbHelper.importFromJson(backupData);
      if (!success) {
        throw Exception('Failed to restore database');
      }

      _progress = 1.0;
      _status = BackupStatus.completed;
      notifyListeners();

      return true;
    } catch (e) {
      _status = BackupStatus.failed;
      _errorMessage = 'Restore failed: $e';
      notifyListeners();
      return false;
    }
  }

  Future<void> deleteBackup(BackupInfo backupInfo) async {
    try {
      // Delete file if not web
      if (!kIsWeb && await File(backupInfo.path).exists()) {
        await File(backupInfo.path).delete();
      }

      // Remove from history
      _backupHistory.removeWhere((backup) => backup.id == backupInfo.id);
      await _saveBackupHistory();
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting backup: $e');
    }
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void resetStatus() {
    _status = BackupStatus.idle;
    _progress = 0.0;
    notifyListeners();
  }

  String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
